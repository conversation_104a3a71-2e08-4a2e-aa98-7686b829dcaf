import { shopifyApi, ApiVersion, LATEST_API_VERSION } from '@shopify/shopify-api';
import '@shopify/shopify-api/adapters/node';

// Shopify API configuration
export const shopify = shopifyApi({
  apiKey: process.env.SHOPIFY_API_KEY!,
  apiSecretKey: process.env.SHOPIFY_SECRET_KEY!,
  scopes: ['read_orders', 'read_products', 'read_customers'],
  hostName: process.env.SHOPIFY_APP_URL || 'localhost:3000',
  apiVersion: LATEST_API_VERSION,
  isEmbeddedApp: false,
  // For private apps, we don't need OAuth
  isPrivateApp: true,
});

// Shopify store configuration
export const SHOPIFY_CONFIG = {
  storeDomain: process.env.SHOPIFY_STORE_DOMAIN!,
  accessToken: process.env.SHOPIFY_ACCESS_TOKEN!,
  apiVersion: '2024-10' as ApiVersion,
};

// Helper function to create REST client
export function createShopifyRestClient() {
  return new shopify.clients.Rest({
    session: {
      shop: SHOPIFY_CONFIG.storeDomain,
      accessToken: SHOPIFY_CONFIG.accessToken,
    },
  });
}

// Simple HTTP client for private apps
export class SimpleShopifyClient {
  private baseUrl: string;
  private headers: Record<string, string>;

  constructor() {
    this.baseUrl = `https://${SHOPIFY_CONFIG.storeDomain}/admin/api/${SHOPIFY_CONFIG.apiVersion}`;
    this.headers = {
      'X-Shopify-Access-Token': SHOPIFY_CONFIG.accessToken,
      'Content-Type': 'application/json',
    };
  }

  async get(path: string, query?: Record<string, string>) {
    const url = new URL(`${this.baseUrl}/${path}.json`);
    if (query) {
      Object.entries(query).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify API Error (${response.status}): ${errorText}`);
    }

    return response.json();
  }

  async post(path: string, data?: Record<string, unknown>) {
    const url = `${this.baseUrl}/${path}.json`;

    const response = await fetch(url, {
      method: 'POST',
      headers: this.headers,
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify API Error (${response.status}): ${errorText}`);
    }

    return response.json();
  }

  async put(path: string, data?: Record<string, unknown>) {
    const url = `${this.baseUrl}/${path}.json`;

    const response = await fetch(url, {
      method: 'PUT',
      headers: this.headers,
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify API Error (${response.status}): ${errorText}`);
    }

    return response.json();
  }
}

export function createSimpleShopifyClient() {
  return new SimpleShopifyClient();
}

// Helper function to create GraphQL client
export function createShopifyGraphQLClient() {
  return new shopify.clients.Graphql({
    session: {
      shop: SHOPIFY_CONFIG.storeDomain,
      accessToken: SHOPIFY_CONFIG.accessToken,
    },
  });
}

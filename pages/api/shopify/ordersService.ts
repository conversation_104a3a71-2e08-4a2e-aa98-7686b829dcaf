import { createShopifyRestClient, createSimpleShopifyClient } from './lib/shopify';

// Shopify Order interface based on Shopify API
export interface ShopifyOrder {
  id: number;
  order_number: number;
  name: string;
  email: string;
  phone: string | null;
  created_at: string;
  updated_at: string;
  cancelled_at: string | null;
  closed_at: string | null;
  processed_at: string | null;
  financial_status: 'pending' | 'authorized' | 'partially_paid' | 'paid' | 'partially_refunded' | 'refunded' | 'voided';
  fulfillment_status: 'fulfilled' | 'null' | 'partial' | 'restocked';
  total_price: string;
  subtotal_price: string;
  total_tax: string;
  currency: string;
  customer: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    phone: string | null;
  } | null;
  billing_address: {
    first_name: string;
    last_name: string;
    address1: string;
    address2: string | null;
    city: string;
    province: string;
    country: string;
    zip: string;
    phone: string | null;
  } | null;
  shipping_address: {
    first_name: string;
    last_name: string;
    address1: string;
    address2: string | null;
    city: string;
    province: string;
    country: string;
    zip: string;
    phone: string | null;
  } | null;
  line_items: Array<{
    id: number;
    product_id: number;
    variant_id: number;
    title: string;
    quantity: number;
    price: string;
    total_discount: string;
    name: string;
    variant_title: string | null;
  }>;
  gateway: string;
  payment_gateway_names: string[];
  tags: string;
  note: string | null;
  source_name: string;
}

// Transformed order interface for our application
export interface OnlineOrder {
  id: string;
  customer: string;
  email: string;
  phone: string;
  items: Array<{
    name: string;
    quantity: number;
    price: string;
  }>;
  total: string;
  status: 'Pending' | 'Processing' | 'Confirmed' | 'Shipped' | 'Delivered' | 'Cancelled';
  orderDate: string;
  platform: 'Shopify' | 'Website' | 'Mobile App' | 'Social Media';
  paymentMethod: string;
  deliveryAddress: string;
  trackingNumber?: string;
  financialStatus: string;
  fulfillmentStatus: string;
  currency: string;
  tags: string[];
  notes?: string;
  source: string;
}

export class ShopifyOrdersService {
  private static client = createSimpleShopifyClient();

  /**
   * Fetch all orders from Shopify
   */
  static async getAllOrders(limit: number = 50): Promise<ShopifyOrder[]> {
    try {
      const response = await this.client.get('orders', {
        limit: limit.toString(),
        status: 'any',
      });

      return response.orders as ShopifyOrder[];
    } catch (error) {
      console.error('Error fetching Shopify orders:', error);
      throw new Error('Failed to fetch orders from Shopify');
    }
  }

  /**
   * Fetch pending orders from Shopify
   */
  static async getPendingOrders(limit: number = 50): Promise<ShopifyOrder[]> {
    try {
      // Get open orders that are not fully fulfilled
      const response = await this.client.get('orders', {
        limit: limit.toString(),
        status: 'open',
        financial_status: 'pending,authorized,partially_paid,paid',
        // Don't filter by fulfillment_status to get unfulfilled orders
      });

      // Filter out fully fulfilled orders manually
      const orders = response.orders as ShopifyOrder[];
      const pendingOrders = orders.filter(order =>
        order.fulfillment_status !== 'fulfilled'
      );

      console.log(`Found ${orders.length} open orders, ${pendingOrders.length} pending orders`);

      return pendingOrders;
    } catch (error) {
      console.error('Error fetching pending Shopify orders:', error);
      throw new Error('Failed to fetch pending orders from Shopify');
    }
  }

  /**
   * Fetch orders by financial status
   */
  static async getOrdersByFinancialStatus(
    status: 'pending' | 'authorized' | 'partially_paid' | 'paid' | 'partially_refunded' | 'refunded' | 'voided',
    limit: number = 50
  ): Promise<ShopifyOrder[]> {
    try {
      const response = await this.client.get('orders', {
        limit: limit.toString(),
        financial_status: status,
      });

      return response.orders as ShopifyOrder[];
    } catch (error) {
      console.error(`Error fetching orders with financial status ${status}:`, error);
      throw new Error(`Failed to fetch orders with financial status ${status}`);
    }
  }

  /**
   * Fetch orders by fulfillment status
   */
  static async getOrdersByFulfillmentStatus(
    status: 'fulfilled' | 'null' | 'partial' | 'restocked',
    limit: number = 50
  ): Promise<ShopifyOrder[]> {
    try {
      const response = await this.client.get('orders', {
        limit: limit.toString(),
        fulfillment_status: status,
      });

      return response.orders as ShopifyOrder[];
    } catch (error) {
      console.error(`Error fetching orders with fulfillment status ${status}:`, error);
      throw new Error(`Failed to fetch orders with fulfillment status ${status}`);
    }
  }

  /**
   * Get a specific order by ID
   */
  static async getOrderById(orderId: number): Promise<ShopifyOrder | null> {
    try {
      const response = await this.client.get(`orders/${orderId}`);

      return response.order as ShopifyOrder;
    } catch (error) {
      console.error(`Error fetching order ${orderId}:`, error);
      return null;
    }
  }

  /**
   * Transform Shopify order to our application format
   */
  static transformShopifyOrder(shopifyOrder: ShopifyOrder): OnlineOrder {
    const customerName = shopifyOrder.customer
      ? `${shopifyOrder.customer.first_name} ${shopifyOrder.customer.last_name}`.trim()
      : 'Guest Customer';

    const deliveryAddress = shopifyOrder.shipping_address
      ? `${shopifyOrder.shipping_address.address1}${shopifyOrder.shipping_address.address2 ? ', ' + shopifyOrder.shipping_address.address2 : ''}, ${shopifyOrder.shipping_address.city}, ${shopifyOrder.shipping_address.province}, ${shopifyOrder.shipping_address.country} ${shopifyOrder.shipping_address.zip}`
      : shopifyOrder.billing_address
      ? `${shopifyOrder.billing_address.address1}${shopifyOrder.billing_address.address2 ? ', ' + shopifyOrder.billing_address.address2 : ''}, ${shopifyOrder.billing_address.city}, ${shopifyOrder.billing_address.province}, ${shopifyOrder.billing_address.country} ${shopifyOrder.billing_address.zip}`
      : 'No address provided';

    // Determine status based on financial and fulfillment status
    let status: OnlineOrder['status'] = 'Pending';
    if (shopifyOrder.cancelled_at) {
      status = 'Cancelled';
    } else if (shopifyOrder.fulfillment_status === 'fulfilled') {
      status = 'Delivered';
    } else if (shopifyOrder.fulfillment_status === 'partial') {
      status = 'Shipped';
    } else if (shopifyOrder.financial_status === 'paid' || shopifyOrder.financial_status === 'authorized') {
      status = 'Processing';
    }

    return {
      id: shopifyOrder.name, // Use order name (e.g., #1001) as display ID
      customer: customerName,
      email: shopifyOrder.email || shopifyOrder.customer?.email || '',
      phone: shopifyOrder.phone || shopifyOrder.customer?.phone || shopifyOrder.shipping_address?.phone || shopifyOrder.billing_address?.phone || '',
      items: shopifyOrder.line_items.map(item => ({
        name: item.title,
        quantity: item.quantity,
        price: item.price,
      })),
      total: shopifyOrder.total_price,
      status,
      orderDate: new Date(shopifyOrder.created_at).toISOString().split('T')[0],
      platform: 'Shopify',
      paymentMethod: shopifyOrder.payment_gateway_names.join(', ') || shopifyOrder.gateway || 'Unknown',
      deliveryAddress,
      financialStatus: shopifyOrder.financial_status,
      fulfillmentStatus: shopifyOrder.fulfillment_status || 'unfulfilled',
      currency: shopifyOrder.currency,
      tags: shopifyOrder.tags ? shopifyOrder.tags.split(', ') : [],
      notes: shopifyOrder.note || undefined,
      source: shopifyOrder.source_name,
    };
  }

  /**
   * Get transformed pending orders for the application
   */
  static async getTransformedPendingOrders(limit: number = 50): Promise<OnlineOrder[]> {
    try {
      const shopifyOrders = await this.getPendingOrders(limit);
      return shopifyOrders.map(order => this.transformShopifyOrder(order));
    } catch (error) {
      console.error('Error getting transformed pending orders:', error);
      throw error;
    }
  }

  /**
   * Get transformed orders by status
   */
  static async getTransformedOrdersByStatus(
    financialStatus?: string,
    fulfillmentStatus?: string,
    limit: number = 50
  ): Promise<OnlineOrder[]> {
    try {
      let shopifyOrders: ShopifyOrder[];

      if (financialStatus && fulfillmentStatus) {
        // If both statuses are specified, we need to fetch all and filter
        const response = await this.client.get('orders', {
          limit: limit.toString(),
          financial_status: financialStatus,
          fulfillment_status: fulfillmentStatus,
        });
        shopifyOrders = response.orders as ShopifyOrder[];
      } else if (financialStatus) {
        shopifyOrders = await this.getOrdersByFinancialStatus(financialStatus as any, limit);
      } else if (fulfillmentStatus) {
        shopifyOrders = await this.getOrdersByFulfillmentStatus(fulfillmentStatus as any, limit);
      } else {
        shopifyOrders = await this.getAllOrders(limit);
      }

      return shopifyOrders.map(order => this.transformShopifyOrder(order));
    } catch (error) {
      console.error('Error getting transformed orders by status:', error);
      throw error;
    }
  }
}

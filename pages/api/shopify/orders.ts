import type { NextApiRequest, NextApiResponse } from 'next';
import { ShopifyOrdersService } from './ordersService';

interface ShopifyError extends Error {
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      const status = req.query.status as string | undefined;
      const financialStatus = req.query.financial_status as string | undefined;
      const fulfillmentStatus = req.query.fulfillment_status as string | undefined;
      const limit = parseInt((req.query.limit as string) || '50');

      let orders;

      if (status === 'pending') {
        orders = await ShopifyOrdersService.getTransformedPendingOrders(limit);
      } else if (financialStatus || fulfillmentStatus) {
        orders = await ShopifyOrdersService.getTransformedOrdersByStatus(
          financialStatus || undefined,
          fulfillmentStatus || undefined,
          limit
        );
      } else {
        const shopifyOrders = await ShopifyOrdersService.getAllOrders(limit);
        orders = shopifyOrders.map(order => ShopifyOrdersService.transformShopifyOrder(order));
      }

      orders.sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime());

      res.status(200).json({
        success: true,
        data: orders,
        total: orders.length,
        message: `Successfully fetched ${orders.length} orders from Shopify`
      });
    } catch (error) {
      const err = error as ShopifyError;
      console.error('Error fetching Shopify orders:', err);
      let errorMessage = 'Failed to fetch orders from Shopify';
      let statusCode = 500;
      if (err instanceof Error) {
        if (err.message.includes('authentication') || err.message.includes('unauthorized')) {
          errorMessage = 'Shopify authentication failed. Please check your API credentials.';
          statusCode = 401;
        } else if (err.message.includes('rate limit')) {
          errorMessage = 'Shopify API rate limit exceeded. Please try again later.';
          statusCode = 429;
        } else if (err.message.includes('network') || err.message.includes('timeout')) {
          errorMessage = 'Network error connecting to Shopify. Please try again.';
          statusCode = 503;
        } else {
          errorMessage = err.message;
        }
      }
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? err : undefined
      });
    }
  } else if (req.method === 'POST') {
    try {
      const { orderId } = req.body;
      if (!orderId) {
        res.status(400).json({ success: false, error: 'Order ID is required' });
        return;
      }
      const shopifyOrder = await ShopifyOrdersService.getOrderById(parseInt(orderId));
      if (!shopifyOrder) {
        res.status(404).json({ success: false, error: 'Order not found' });
        return;
      }
      const transformedOrder = ShopifyOrdersService.transformShopifyOrder(shopifyOrder);
      res.status(200).json({
        success: true,
        data: transformedOrder,
        message: 'Order fetched successfully'
      });
    } catch (error) {
      const err = error as ShopifyError;
      console.error('Error fetching specific Shopify order:', err);
      let errorMessage = 'Failed to fetch order from Shopify';
      if (err instanceof Error) {
        errorMessage = err.message;
      }
      res.status(500).json({
        success: false,
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? err : undefined
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

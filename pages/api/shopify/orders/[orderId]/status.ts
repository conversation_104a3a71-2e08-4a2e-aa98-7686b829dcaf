import type { NextApiRequest, NextApiResponse } from 'next';
import { createSimpleShopifyClient } from '../../lib/shopify';

interface ShopifyError extends Error {
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { orderId } = req.query;
  const { status } = req.body;

  if (!orderId || !status) {
    return res.status(400).json({ error: 'Order ID and status are required' });
  }

  try {
    const client = createSimpleShopifyClient();
    
    // Map our status to Shopify fulfillment status
    switch (status) {
      case 'Processing':
        // Order is being processed, no fulfillment yet
        break;
      case 'Shipped':
        // fulfillmentStatus = 'partial'; // or 'fulfilled' depending on your logic
        break;
      case 'Delivered':
        // fulfillmentStatus = 'fulfilled';
        break;
      default:
        return res.status(400).json({ error: 'Invalid status' });
    }

    // Note: Shopify doesn't allow direct status updates like this
    // You would typically need to create fulfillments or update specific fields
    // This is a simplified example - in practice you'd need to:
    // 1. Create a fulfillment for 'Shipped' status
    // 2. Update fulfillment status for 'Delivered'
    // 3. Add order notes or tags to track custom statuses

    // For now, we'll add a note to the order to track our custom status
    const noteResponse = await client.put(`orders/${orderId}`, {
      order: {
        id: orderId,
        note: `Status updated to: ${status} at ${new Date().toISOString()}`,
        tags: `delivery-status-${status.toLowerCase()}`
      }
    });

    res.status(200).json({
      success: true,
      message: `Order ${orderId} status updated to ${status}`,
      data: noteResponse
    });

  } catch (error) {
    const err = error as ShopifyError;
    console.error('Error updating order status:', err);
    res.status(500).json({
      error: 'Failed to update order status',
      message: err.message
    });
  }
}

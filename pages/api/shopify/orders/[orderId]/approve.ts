import type { NextApiRequest, NextApiResponse } from 'next';
import { createSimpleShopifyClient } from '../../lib/shopify';

interface ShopifyError extends Error {
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { orderId } = req.query;

  if (!orderId) {
    return res.status(400).json({ error: 'Order ID is required' });
  }

  try {
    const client = createSimpleShopifyClient();
    
    // Add a note to the order indicating approval
    const approvalNote = `Order approved for processing at ${new Date().toISOString()}`;
    
    const response = await client.put(`orders/${orderId}`, {
      order: {
        id: orderId,
        note: approvalNote,
        tags: 'approved,processing'
      }
    });

    res.status(200).json({
      success: true,
      message: `Order ${orderId} approved successfully`,
      data: response
    });

  } catch (error) {
    const err = error as ShopifyError;
    console.error('Error approving order:', err);
    res.status(500).json({
      error: 'Failed to approve order',
      message: err.message
    });
  }
}

# Email Notifications Setup Guide

This guide explains how to configure email notifications for order deliveries and cancellations in the Zawaya Delivery system.

## Overview

The system automatically sends email notifications to `<EMAIL>` when:
- An order status is changed to "Delivered"
- An order status is changed to "Cancelled"

## Email Configuration

### 1. Environment Variables

Create a `.env.local` file in the project root with the following variables:

```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### 2. Gmail Setup (Recommended)

If using Gmail as your SMTP provider:

1. **Enable 2-Factor Authentication**
   - Go to your Google Account settings
   - Enable 2-factor authentication

2. **Generate App Password**
   - Visit: https://support.google.com/accounts/answer/185833
   - Generate a new app password for "Mail"
   - Use this app password as `EMAIL_PASS` (not your regular Gmail password)

3. **Configure Environment Variables**
   ```env
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_SECURE=false
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-16-character-app-password
   ```

### 3. Other Email Providers

For other email providers, adjust the settings accordingly:

**Outlook/Hotmail:**
```env
EMAIL_HOST=smtp-mail.outlook.com
EMAIL_PORT=587
EMAIL_SECURE=false
```

**Yahoo:**
```env
EMAIL_HOST=smtp.mail.yahoo.com
EMAIL_PORT=587
EMAIL_SECURE=false
```

**Custom SMTP:**
```env
EMAIL_HOST=your-smtp-server.com
EMAIL_PORT=587
EMAIL_SECURE=false
```

## Email Templates

The system includes two email templates:

### Delivered Order Email
- **Subject:** "✅ Order [ORDER_ID] Successfully Delivered - Zawaya Stores"
- **Content:** Complete order details, customer information, delivery performance metrics
- **Color Scheme:** Orange/amber theme

### Cancelled Order Email
- **Subject:** "❌ Order [ORDER_ID] Cancelled - Zawaya Stores"
- **Content:** Order details, cancellation reason, financial impact, next steps
- **Color Scheme:** Red theme

## Testing

To test email notifications:

1. Ensure environment variables are configured
2. Start the development server: `npm run dev`
3. Create a test order
4. Change the order status to "Delivered" or "Cancelled"
5. Check the console for email sending logs
6. Verify email delivery to the configured recipient

## Troubleshooting

### Common Issues

1. **"Authentication failed" error**
   - Verify EMAIL_USER and EMAIL_PASS are correct
   - For Gmail, ensure you're using an app password, not your regular password
   - Check that 2-factor authentication is enabled

2. **"Connection timeout" error**
   - Verify EMAIL_HOST and EMAIL_PORT are correct
   - Check firewall settings
   - Try different EMAIL_SECURE settings (true/false)

3. **Emails not being sent**
   - Check console logs for error messages
   - Verify environment variables are loaded correctly
   - Ensure the email service is not blocked by your hosting provider

### Debug Mode

To enable detailed email debugging, check the console logs when orders are updated. The system will log:
- ✅ Successful email sends with message ID
- ❌ Failed email attempts with error details
- ℹ️ Skipped emails when credentials are not configured

## Security Notes

- Never commit actual email credentials to version control
- Use environment variables for all sensitive configuration
- Consider using dedicated email accounts for system notifications
- Regularly rotate email passwords/app passwords

## Customization

To modify email templates or add more recipients:

1. Edit `src/services/email/emailService.ts`
2. Modify the `generateDeliveredEmailTemplate()` or `generateCancelledEmailTemplate()` methods
3. Update the recipient email in the API route: `src/app/api/orders/route.ts`

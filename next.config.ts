import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/config.ts');

const nextConfig: NextConfig = {
  // Reduce console warnings in development
  logging: {
    fetches: {
      fullUrl: false,
    },
  },

  // Experimental features for better performance
  experimental: {
    optimizePackageImports: ['react-icons', 'lucide-react'],
  },

  // Webpack configuration to handle lodash properly
  webpack: (config, { isServer }) => {
    // Handle lodash bundling issues
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    return config;
  },

  // Turbopack configuration (stable as of Next.js 15)
  turbopack: {
    // Enable Turbopack for development when using --turbopack flag
  },
};

export default withNextIntl(nextConfig);

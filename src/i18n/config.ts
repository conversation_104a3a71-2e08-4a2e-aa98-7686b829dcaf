
import { getRequestConfig } from 'next-intl/server';

// Can be imported from a shared config
export const locales = ['en', 'ar'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'en';

export default getRequestConfig(async ({ requestLocale }) => {
  // This typically corresponds to the `[locale]` segment
  let locale = await requestLocale;

  // Ensure that a valid locale is used
  if (!locale || !locales.includes(locale as Locale)) {
    locale = defaultLocale;
  }

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default
  };
});

// Helper function to get locale direction - FORCE LTR FOR ALL LANGUAGES
export function getLocaleDirection(): 'ltr' | 'rtl' {
  return 'ltr'; // Always return LTR for both English and Arabic
}

// Helper function to get locale display name
export function getLocaleDisplayName(locale: string): string {
  const displayNames: Record<string, string> = {
    en: 'English',
    ar: 'العربية'
  };
  return displayNames[locale] || locale;
}

import { Geist, <PERSON>eist_Mono, Poppins } from "next/font/google";

// Primary fonts - preloaded
export const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
  preload: true,
});

export const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
  preload: false, // Only preload if actually used
});

// Poppins - only essential weights preloaded
export const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600"], // Most commonly used weights
  display: "swap",
  preload: true,
});

// Additional Poppins weights - loaded on demand
export const poppinsLight = Poppins({
  variable: "--font-poppins-light",
  subsets: ["latin"],
  weight: ["300"],
  display: "swap",
  preload: false,
});

export const poppinsBold = Poppins({
  variable: "--font-poppins-bold",
  subsets: ["latin"],
  weight: ["700"],
  display: "swap",
  preload: false,
});

// Font class names for easy usage
export const fontClasses = {
  sans: geistSans.variable,
  mono: geistMono.variable,
  poppins: poppins.variable,
  poppinsLight: poppinsLight.variable,
  poppinsBold: poppinsBold.variable,
};

// Combined class string for layout
export const allFontVariables = `${geistSans.variable} ${geistMono.variable} ${poppins.variable} ${poppinsLight.variable} ${poppinsBold.variable}`;

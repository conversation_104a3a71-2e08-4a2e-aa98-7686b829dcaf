// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getMessaging } from "firebase/messaging";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyDGZ5lIgPxC1c2FksxIa8Ak7ngehw-G3n8",
  authDomain: "web-delivery-61cae.firebaseapp.com",
  projectId: "web-delivery-61cae",
  storageBucket: "web-delivery-61cae.firebasestorage.app",
  messagingSenderId: "158352075738",
  appId: "1:158352075738:web:c83d5ef788fcd6865b6ee3",
  measurementId: "G-9QFNF53DJY"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Initialize Analytics (only in browser environment)
export const analytics = typeof window !== 'undefined' ? getAnalytics(app) : null;

// Initialize Messaging (only in browser environment)
export const messaging = typeof window !== 'undefined' ? getMessaging(app) : null;

export default app;

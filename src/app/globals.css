@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* FORCE LTR FOR ALL LANGUAGES */
html, body, [dir="rtl"], [dir="ltr"], html[dir="rtl"], html[dir="ltr"], body[dir="rtl"], body[dir="ltr"] {
  direction: ltr !important;
  text-align: left !important;
}

/* Force LTR based on classes */
html.rtl, html.ltr, body.rtl, body.ltr, .rtl, .ltr {
  direction: ltr !important;
  text-align: left !important;
}

/* Enhanced font support for Arabic in LTR mode */
[lang="ar"], html[lang="ar"], body[lang="ar"] {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
  direction: ltr !important;
  text-align: left !important;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-poppins: var(--font-poppins);
}

/* Disabled dark mode to prevent black background issues */
/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-display: swap;
}

/* Force light mode to prevent black background issues */
html, body {
  background-color: #ffffff !important;
  color: #171717 !important;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Hide scrollbars utility class */
.scrollbar-hide {
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideInUp {
  animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideInDown {
  animation: slideInDown 0.5s ease-out forwards;
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out forwards;
}

/* Enhanced hover effects */
.hover\:scale-102:hover {
  transform: scale(1.02);
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slideInLeft {
  animation: slideInFromLeft 0.7s ease-out forwards;
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slideInRight {
  animation: slideInFromRight 0.7s ease-out forwards;
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-slideInUp {
  animation: slideInUp 0.4s ease-out forwards;
}

@keyframes overlayFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.animate-overlayFadeIn {
  animation: overlayFadeIn 0.3s ease-out forwards;
}

/* Login form specific styles */
.cloud-border {
  clip-path: url(#cloud-border);
}

/* Custom checkbox styling */
input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

input[type="checkbox"]:checked {
  background-color: #8b5cf6;
  border-color: #8b5cf6;
}

input[type="checkbox"]:checked::after {
  content: '✓';
  color: white;
  font-size: 12px;
  position: absolute;
}

/* Enhanced box shadows */
.shadow-soft {
  box-shadow: 0 10px 25px -5px rgba(124, 58, 237, 0.1), 0 8px 10px -6px rgba(124, 58, 237, 0.05);
}

.wave-divider {
  filter: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
}

/* Override browser autocomplete styling - comprehensive approach */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #374151 !important;
  background-color: white !important;
  background-image: none !important;
  border: none !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* For Firefox */
input:-moz-autofill {
  background-color: white !important;
  color: #374151 !important;
  background-image: none !important;
  border: none !important;
}

/* General input field styling to ensure white background */
input[type="email"],
input[type="password"],
input[type="text"] {
  background-color: transparent !important;
  background-image: none !important;
}

/* Additional override for any remaining autocomplete styling */
input:-internal-autofill-selected {
  background-color: white !important;
  background-image: none !important;
}

/* Force remove any yellow highlighting */
input:autofill {
  background-color: white !important;
  background-image: none !important;
}

/* Enhanced popup and modal animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progress {
  0% {
    width: 100%;
  }
  100% {
    width: 0%;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.3s ease-out forwards;
}

.animate-progress {
  animation: progress linear forwards;
}

.animate-pulse-slow {
  animation: pulse-slow 2s ease-in-out infinite;
}

/* Page Transition Animations */
@keyframes pageSlideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pageSlideOutLeft {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-30px);
  }
}

@keyframes pageFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pageFadeOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
}

.page-enter {
  animation: pageFadeIn 0.4s ease-out forwards;
}

.page-exit {
  animation: pageFadeOut 0.3s ease-in forwards;
}

.page-slide-enter {
  animation: pageSlideInRight 0.4s ease-out forwards;
}

.page-slide-exit {
  animation: pageSlideOutLeft 0.3s ease-in forwards;
}

/* Navigation transition states */
.nav-transitioning {
  pointer-events: none;
}

.nav-transitioning * {
  cursor: wait !important;
}

/* Prevent initial flash of unstyled content */
[data-page-content] {
  opacity: 1;
  transform: translateY(0);
}

/* Ensure smooth initial render */
.page-transition-wrapper {
  opacity: 1;
  transition: opacity 0.1s ease-in-out;
}

/* Layout fixes for header and sidebar positioning */
@media (min-width: 1024px) {
  /* Ensure main content area accounts for sidebar width */
  .main-content-with-sidebar {
    margin-left: 16rem; /* 256px sidebar width */
  }

  /* Ensure header doesn't overlap sidebar */
  .header-with-sidebar {
    left: 16rem; /* 256px sidebar width */
  }
}

/* Mobile-specific responsive utilities */
@media (max-width: 640px) {
  .mobile-scroll-x {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-text-sm {
    font-size: 0.875rem;
  }

  .mobile-p-2 {
    padding: 0.5rem;
  }

  .mobile-gap-2 {
    gap: 0.5rem;
  }

  /* Hide elements on mobile */
  .mobile-hidden {
    display: none;
  }

  /* Stack elements on mobile */
  .mobile-stack {
    flex-direction: column;
  }

  /* Full width on mobile */
  .mobile-full {
    width: 100%;
  }
}

/* Tablet-specific styles */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Enhanced Dropdown List Styling */
select.modern-select {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  cursor: pointer;
}

select.modern-select:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

select.modern-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

select.modern-select:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
}

select.modern-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f9fafb;
  transform: none;
}

/* Enhanced Dropdown Options Styling */
select.modern-select option {
  padding: 14px 16px;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  color: #374151;
  font-weight: 500;
  font-size: 14px;
  border: none;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  line-height: 1.5;
}

select.modern-select option:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #1f2937;
  transform: translateX(2px);
  box-shadow: inset 3px 0 0 #6366f1;
}

select.modern-select option:checked,
select.modern-select option:focus {
  background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
  color: #4338ca;
  font-weight: 600;
  box-shadow: inset 3px 0 0 #6366f1;
}

select.modern-select option:active {
  background: linear-gradient(135deg, #ddd6fe 0%, #c7d2fe 100%);
  color: #3730a3;
}

/* Webkit specific dropdown styling */
select.modern-select::-webkit-scrollbar {
  width: 8px;
}

select.modern-select::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

select.modern-select::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

select.modern-select::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

/* Firefox specific dropdown styling */
@-moz-document url-prefix() {
  select.modern-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
  }

  select.modern-select option {
    background-color: white;
    color: #374151;
  }

  select.modern-select option:hover {
    background-color: #f8fafc;
    color: #1f2937;
  }

  select.modern-select option:checked {
    background-color: #eef2ff;
    color: #4338ca;
  }
}

/* Enhanced dropdown animation on open */
select.modern-select[size]:not([size="1"]),
select.modern-select[multiple] {
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  animation: dropdownSlideDown 0.2s ease-out;
}

@keyframes dropdownSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Custom Dropdown Animations */
@keyframes dropdown-slide-down {
  from {
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes check-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-3px) scale(1.1);
  }
  60% {
    transform: translateY(-1px) scale(1.05);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.animate-dropdown-slide-down {
  animation: dropdown-slide-down 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-check-bounce {
  animation: check-bounce 0.6s ease-out;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Custom Scrollbar for Dropdown */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 3px;
  transition: all 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  transform: scaleY(1.1);
}

/* Enhanced Dropdown List Item Styling */
.dropdown-item {
  position: relative;
  overflow: hidden;
}

.dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.5s ease;
}

.dropdown-item:hover::before {
  left: 100%;
}

/* Gradient border animation for focused dropdown */
.dropdown-focused {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.dropdown-focused::before {
  content: '';
  position: absolute;
  inset: -2px;
  padding: 2px;
  background: linear-gradient(45deg, #6366f1, #8b5cf6, #ec4899, #f59e0b, #10b981, #6366f1);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  animation: gradient-rotate 2s linear infinite;
}

@keyframes gradient-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notification dropdown animation */
@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Smooth scrollbar for notification list */
.notification-scroll::-webkit-scrollbar {
  width: 4px;
}

.notification-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.notification-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.notification-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

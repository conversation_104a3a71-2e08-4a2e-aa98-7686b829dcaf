'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import { useLocale } from '@/contexts/LocaleContext';

import { ForgotPasswordModal } from '@/components/ForgotPasswordModal';
import { PageTransition } from '@/components/PageTransition';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';

export default function LoginPage() {
  const router = useRouter();
  const {
    signIn,
    user,
    signInWithGoogle,
    getRememberedEmail
  } = useAuth();

  const t = useAuthTranslations();
  const common = useCommonTranslations();
  const { locale } = useLocale();

  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [errors, setErrors] = useState({ email: '', password: '', general: '' });
  const [touched, setTouched] = useState({ email: false, password: false });
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  // Track if component has mounted to prevent hydration mismatch
  const [isMounted, setIsMounted] = useState(false);

  // Track mounted state to prevent hydration mismatch
  React.useEffect(() => {
    setIsMounted(true);
  }, []);

  // Redirect if user is already logged in (but only after mounting to prevent issues)
  React.useEffect(() => {
    if (isMounted && user && !isLoading) {
      // Extract locale from current path
      const currentLocale = window.location.pathname.split('/')[1] || 'en';
      router.push(`/${currentLocale}/dashboard`);
    }
  }, [user, router, isMounted, isLoading]);

  // Reset navigation state on component unmount
  React.useEffect(() => {
    return () => {
      setIsNavigating(false);
    };
  }, []);

  // Load remembered email on component mount
  React.useEffect(() => {
    if (isMounted) {
      const loadRememberedEmail = async () => {
        try {
          const rememberedEmail = await getRememberedEmail();
          if (rememberedEmail) {
            setEmail(rememberedEmail);
            setRememberMe(true);
          }
        } catch (error) {
          console.error('Error loading remembered email:', error);
        }
      };

      loadRememberedEmail();
    }
  }, [isMounted, getRememberedEmail]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setHasSubmitted(true);

    if (!email || !password) {
      setErrors({
        email: !email ? t('errors.emailRequired') : '',
        password: !password ? t('errors.passwordRequired') : '',
        general: ''
      });
      return;
    }

    setIsLoading(true);
    setErrors({ email: '', password: '', general: '' });

    try {
      await signIn(email, password, rememberMe);
      // User will be redirected by the useEffect hook
    } catch (error: unknown) {
      console.error('Login error:', error);

      let errorMessage = t('errors.loginError');
      if (error && typeof error === 'object' && 'code' in error) {
        const firebaseError = error as { code: string };
        if (firebaseError.code === 'auth/user-not-found' || firebaseError.code === 'auth/wrong-password') {
          errorMessage = t('errors.invalidCredentials');
        }
      }

      setErrors({ email: '', password: '', general: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFieldBlur = (field: 'email' | 'password') => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  const shouldShowError = (field: 'email' | 'password') => {
    return !isNavigating && (hasSubmitted || touched[field]);
  };

  // Navigation transition hook
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  const handleNavigation = (path: string) => {
    setIsNavigating(true);
    // Clear validation state before navigation
    setErrors({ email: '', password: '', general: '' });
    setTouched({ email: false, password: false });
    setHasSubmitted(false);

    // Clear form fields when navigating to register to prevent confusion
    if (path === '/register') {
      setEmail('');
      setPassword('');
      setRememberMe(false);
    }

    navigateWithTransition(path);
  };



  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true);
    setErrors({ email: '', password: '', general: '' });

    try {
      await signInWithGoogle(rememberMe);
      // User will be redirected by the useEffect hook
    } catch (error: unknown) {
      console.error('Google login error:', error);

      let errorMessage = t('errors.googleSignInFailed');
      if (error && typeof error === 'object' && 'code' in error) {
        const firebaseError = error as { code: string };
        if (firebaseError.code === 'auth/popup-closed-by-user') {
          errorMessage = t('errors.signInCancelled');
        } else if (firebaseError.code === 'auth/popup-blocked') {
          errorMessage = t('errors.popupBlocked');
        }
      }

      setErrors({ email: '', password: '', general: errorMessage });
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <PageTransition transitionKey="login" animationType="fade">
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-blue-100 p-4 relative overflow-hidden animate-fadeIn" data-page-content>
      {/* Background cloud decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <svg className="absolute top-0 right-0 w-96 h-96 opacity-10" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M100 200C100 155.817 135.817 120 180 120C224.183 120 260 155.817 260 200C304.183 200 340 235.817 340 280C340 324.183 304.183 360 260 360H140C95.8172 360 60 324.183 60 280C60 235.817 95.8172 200 140 200H100Z" fill="url(#gradient1)"/>
          <defs>
            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.1"/>
              <stop offset="100%" stopColor="#a855f7" stopOpacity="0.05"/>
            </linearGradient>
          </defs>
        </svg>
        <svg className="absolute bottom-0 left-0 w-80 h-80 opacity-10" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M80 250C80 205.817 115.817 170 160 170C204.183 170 240 205.817 240 250C284.183 250 320 285.817 320 330C320 374.183 284.183 410 240 410H120C75.8172 410 40 374.183 40 330C40 285.817 75.8172 250 120 250H80Z" fill="url(#gradient2)"/>
          <defs>
            <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.08"/>
              <stop offset="100%" stopColor="#a855f7" stopOpacity="0.03"/>
            </linearGradient>
          </defs>
        </svg>
      </div>

      <div className="w-full max-w-6xl overflow-hidden bg-white rounded-3xl shadow-xl flex relative z-10">
        {/* Left side - Login form */}
        <div className="w-full md:w-1/2 p-8 md:p-12 relative z-10 animate-slideInLeft">
          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold mb-1">{t('welcomeBack')}</h1>
              <p className="text-gray-600">{t('loginToAccount')}</p>
            </div>

            <form onSubmit={handleSubmit}>
              {/* Email Input */}
              <div className="mb-6">
                <div className="flex items-center bg-gray-50 rounded-3xl shadow-md overflow-hidden border border-gray-100 focus-within:border-purple-300 focus-within:shadow-lg transition-all duration-200">
                  <div className="pl-4">
                    <div className="w-9 h-9 flex items-center justify-center bg-gradient-to-br from-purple-500 to-purple-700 rounded-full shadow-lg shadow-purple-300 border border-white/50">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M22 7L13.03 12.7C12.7213 12.8934 12.3643 12.996 12 12.996C11.6357 12.996 11.2787 12.8934 10.97 12.7L2 7" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <input
                    type="email"
                    placeholder={t('email')}
                    className="w-full py-5 px-4 outline-none text-gray-700 bg-transparent placeholder-gray-400"
                    style={{
                      backgroundColor: 'transparent !important',
                      backgroundImage: 'none !important',
                      boxShadow: 'none !important'
                    }}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    onBlur={() => handleFieldBlur('email')}
                    autoComplete="email"
                  />
                </div>
                {shouldShowError('email') && errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
              </div>

              {/* Password Input */}
              <div className="mb-4">
                <div className="flex items-center bg-gray-50 rounded-3xl shadow-md overflow-hidden border border-gray-100 focus-within:border-purple-300 focus-within:shadow-lg transition-all duration-200">
                  <div className="pl-4">
                    <div className="w-9 h-9 flex items-center justify-center bg-gradient-to-br from-purple-500 to-purple-700 rounded-full shadow-lg shadow-purple-300 border border-white/50">
                      <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15.8333 9.16667H4.16667C3.24619 9.16667 2.5 9.91286 2.5 10.8333V16.6667C2.5 17.5871 3.24619 18.3333 4.16667 18.3333H15.8333C16.7538 18.3333 17.5 17.5871 17.5 16.6667V10.8333C17.5 9.91286 16.7538 9.16667 15.8333 9.16667Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M5.83337 9.16667V5.83334C5.83337 4.72827 6.27236 3.66846 7.05376 2.88706C7.83516 2.10566 8.89497 1.66667 10.0001 1.66667C11.1051 1.66667 12.1649 2.10566 12.9463 2.88706C13.7277 3.66846 14.1667 4.72827 14.1667 5.83334V9.16667" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    placeholder={t('password')}
                    className="w-full py-5 px-4 outline-none text-gray-700 bg-transparent placeholder-gray-400"
                    style={{
                      backgroundColor: 'transparent !important',
                      backgroundImage: 'none !important',
                      boxShadow: 'none !important'
                    }}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    onBlur={() => handleFieldBlur('password')}
                    autoComplete="current-password"
                  />
                  <button
                    type="button"
                    className="pr-5 text-purple-400 hover:text-purple-600 transition-colors"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      {showPassword ? (
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      ) : (
                        <>
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </>
                      )}
                    </svg>
                  </button>
                </div>
                {shouldShowError('password') && errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
              </div>

              {/* Remember Me and Forgot Password */}
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="remember"
                    className="h-4 w-4"
                    checked={rememberMe}
                    onChange={() => setRememberMe(!rememberMe)}
                  />
                  <label htmlFor="remember" className="ml-2 text-sm text-gray-500">
                    {t('rememberMe')}
                  </label>
                </div>
                <button
                  type="button"
                  onClick={() => setShowForgotPassword(true)}
                  className="text-sm text-purple-600 hover:underline"
                >
                  {t('forgotPassword')}
                </button>
              </div>

              {/* Error Messages */}
              {errors.general && (
                <div className="mb-4 text-center">
                  <p className="text-red-500">{errors.general}</p>
                </div>
              )}

              {/* Sign In Button */}
              <div className="flex justify-center">
                <button
                  type="submit"
                  className="bg-gradient-to-r from-purple-500 to-purple-600 text-white py-3 px-12 rounded-full font-semibold text-sm tracking-wide hover:from-purple-600 hover:to-purple-700 transition duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
                  disabled={isLoading || isGoogleLoading}
                >
                  {isLoading ? `${t('signIn')}...` : t('signIn').toUpperCase()}
                </button>
              </div>



              {/* Google Login Section */}
              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span
                      className="px-2 bg-white text-gray-500"
                      style={{
                        direction: locale === 'ar' ? 'rtl' : 'ltr',
                        textAlign: 'center'
                      }}
                    >
                      {t('orContinueWith')}
                    </span>
                  </div>
                </div>

                <div className="mt-6 flex justify-center">
                  <button
                    type="button"
                    onClick={handleGoogleLogin}
                    disabled={isLoading || isGoogleLoading}
                    className="flex items-center justify-center gap-3 w-full bg-white border border-gray-300 text-gray-700 py-3 px-6 rounded-full font-medium hover:bg-gray-50 hover:border-gray-400 transition duration-300 shadow-sm hover:shadow-md transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {isGoogleLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600"></div>
                        <span
                          style={{
                            direction: locale === 'ar' ? 'rtl' : 'ltr'
                          }}
                        >
                          {t('signingInWithGoogle')}
                        </span>
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5" viewBox="0 0 24 24">
                          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <span
                          style={{
                            direction: locale === 'ar' ? 'rtl' : 'ltr'
                          }}
                        >
                          {t('continueWithGoogle')}
                        </span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Sign Up Link */}
              <div className="text-center mt-6">
                <p
                  className="text-gray-600 text-sm"
                  style={{
                    direction: locale === 'ar' ? 'rtl' : 'ltr',
                    textAlign: 'center'
                  }}
                >
                  {t('dontHaveAccount')} <button onClick={() => handleNavigation('/register')} className="text-purple-600 hover:underline">{t('signUp')}</button>
                </p>
              </div>
            </form>
          </div>
        </div>

        {/* SVG Definitions */}
        <svg className="absolute" width="0" height="0">
          <defs>
            <clipPath id="cloud-border" clipPathUnits="objectBoundingBox">
              <path d="M0.5,0 C0.65,0.05 0.75,0.15 1,0 V1 H0 V0 C0.2,0.15 0.35,0.05 0.5,0" />
            </clipPath>
          </defs>
        </svg>

        {/* Wave divider */}
        <div className="absolute top-0 bottom-0 left-1/2 w-full h-full overflow-hidden pointer-events-none wave-divider">
          <svg className="absolute h-full" viewBox="0 0 100 100" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0,0 C20,5 35,15 50,0 C65,15 80,5 100,0 L100,100 L0,100 Z" fill="white"/>
          </svg>
        </div>

        {/* Right side - Welcome Back section with purple gradient and cloud border */}
        <div className="hidden md:block md:w-1/2 bg-gradient-to-br from-purple-500 to-purple-700 relative overflow-hidden animate-slideInRight">
          {/* Enhanced Cloud overlays */}
          <div className="absolute inset-0">
            {/* Top cloud border */}
            <svg className="absolute top-0 left-0 w-full h-40" viewBox="0 0 400 120" preserveAspectRatio="none" fill="white" xmlns="http://www.w3.org/2000/svg">
              <path d="M0,0 C40,40 80,20 120,35 C160,50 200,25 240,15 C280,5 320,25 360,35 C380,40 390,35 400,30 L400,120 L0,120 Z" opacity="0.3"/>
            </svg>

            {/* Bottom cloud border */}
            <svg className="absolute bottom-0 left-0 w-full h-40" viewBox="0 0 400 120" preserveAspectRatio="none" fill="white" xmlns="http://www.w3.org/2000/svg">
              <path d="M0,120 C40,80 80,100 120,85 C160,70 200,95 240,105 C280,115 320,95 360,85 C380,80 390,85 400,90 L400,0 L0,0 Z" opacity="0.3"/>
            </svg>

            {/* Additional decorative clouds */}
            <svg className="absolute top-1/4 right-8 w-32 h-32 opacity-20" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M50 100C50 77.909 67.909 60 90 60C112.091 60 130 77.909 130 100C152.091 100 170 117.909 170 140C170 162.091 152.091 180 130 180H70C47.909 180 30 162.091 30 140C30 117.909 47.909 100 70 100H50Z" fill="white"/>
            </svg>

            <svg className="absolute bottom-1/4 left-8 w-24 h-24 opacity-15" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M60 120C60 97.909 77.909 80 100 80C122.091 80 140 97.909 140 120C162.091 120 180 137.909 180 160C180 182.091 162.091 200 140 200H80C57.909 200 40 182.091 40 160C40 137.909 57.909 120 80 120H60Z" fill="white"/>
            </svg>
          </div>

          {/* Content */}
          <div className="relative z-10 p-12 text-white flex flex-col justify-center h-full">
            <div className="max-w-md mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4">{t('welcomeBack')}!</h2>
              <p className="mb-6 text-white/80 leading-relaxed">
                {t('loginDescription')}
              </p>
            </div>
          </div>
        </div>
      </div>



      {/* Forgot Password Modal */}
      <ForgotPasswordModal
        isOpen={showForgotPassword}
        onClose={() => setShowForgotPassword(false)}
      />

      {/* Navigation Loading Overlay */}
      {isNavigating && (
        <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4"></div>
            <p className="text-gray-600 font-medium">{common('loading')}</p>
          </div>
        </div>
      )}
      </div>
    </PageTransition>
  );
}

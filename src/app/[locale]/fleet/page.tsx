'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import { useAuth } from '@/contexts/AuthContext';
import { useFleetTranslations, useCommonTranslations, useConfirmationTranslations, useToastTranslations } from '@/hooks/useTranslations';
import { useConfirmationDialog } from '@/components/ui/ConfirmationDialog';
import { useToastHelpers } from '@/components/ui/Toast';
import {
  Truck,
  Plus,
  Edit2,
  Trash2,
  Phone,
  Mail,
  UserCheck,
  UserX,
  Search,
  Package,
  X,
  Users,
  Wrench,
  Calendar,
  Fuel,
  Gauge,
  Eye,
  AlertTriangle,
  Link,
  Unlink
} from 'lucide-react';

import { SettingsModal } from '@/components/SettingsModal';
import { PageTransition } from '@/components/PageTransition';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';
import {
  CompactModal,
  TwoColumnForm,
  FormField,
  IconInput,
  IconSelect,
  ModalActions,
} from '@/components/CompactModal';

// Fleet types
type TruckStatus = 'Active' | 'Maintenance' | 'Inactive';
type DriverStatus = 'Available' | 'On Duty' | 'Off Duty' | 'Inactive';

type Truck = {
  id: string;
  licensePlate: string;
  model: string;
  year: number;
  capacity: string;
  status: TruckStatus;
  driverId?: string;
  driverName?: string;
  lastMaintenance: string;
  nextMaintenance: string;
  mileage: number;
  fuelType: 'Diesel' | 'Gasoline' | 'Electric';
};

type Driver = {
  id: string;
  name: string;
  licenseNumber: string;
  phone: string;
  email: string;
  status: DriverStatus;
  truckId?: string;
  truckLicensePlate?: string;
  joinDate: string;
  licenseExpiry: string;
  experience: number; // years
};

// Add/Edit Truck Modal Component
const AddEditTruckModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (truck: Omit<Truck, 'id'>) => void;
  editingTruck?: Truck | null;
}> = ({ isOpen, onClose, onSave, editingTruck }) => {
  const t = useFleetTranslations();
  const common = useCommonTranslations();

  const [formData, setFormData] = useState({
    licensePlate: '',
    model: '',
    year: new Date().getFullYear(),
    capacity: '',
    status: 'Active' as TruckStatus,
    fuelType: 'Diesel' as 'Diesel' | 'Gasoline' | 'Electric',
    lastMaintenance: new Date().toISOString().split('T')[0],
    nextMaintenance: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    mileage: 0
  });

  useEffect(() => {
    if (editingTruck) {
      setFormData({
        licensePlate: editingTruck.licensePlate,
        model: editingTruck.model,
        year: editingTruck.year,
        capacity: editingTruck.capacity,
        status: editingTruck.status,
        fuelType: editingTruck.fuelType,
        lastMaintenance: editingTruck.lastMaintenance,
        nextMaintenance: editingTruck.nextMaintenance,
        mileage: editingTruck.mileage
      });
    } else {
      setFormData({
        licensePlate: '',
        model: '',
        year: new Date().getFullYear(),
        capacity: '',
        status: 'Active',
        fuelType: 'Diesel',
        lastMaintenance: new Date().toISOString().split('T')[0],
        nextMaintenance: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        mileage: 0
      });
    }
  }, [editingTruck, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  return (
    <CompactModal
      isOpen={isOpen}
      onClose={onClose}
      title={editingTruck ? t('truckDetails') : t('addNewTruck')}
      maxWidth="max-w-xl"
    >
      <form onSubmit={handleSubmit} className="space-y-3">
        <TwoColumnForm>
          <FormField label={`${t('licensePlate')} *`}>
            <IconInput
              icon={<Truck className="w-4 h-4" />}
              type="text"
              required
              value={formData.licensePlate}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, licensePlate: e.target.value })}
              placeholder="ABC-123"
            />
          </FormField>

          <FormField label={`${t('model')} *`}>
            <IconInput
              icon={<Package className="w-4 h-4" />}
              type="text"
              required
              value={formData.model}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, model: e.target.value })}
              placeholder="Ford Transit"
            />
          </FormField>

          <FormField label={`${t('year')} *`}>
            <IconInput
              icon={<Calendar className="w-4 h-4" />}
              type="number"
              required
              min="1990"
              max={new Date().getFullYear() + 1}
              value={formData.year}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, year: parseInt(e.target.value) })}
            />
          </FormField>

          <FormField label={`${t('capacity')} *`}>
            <IconInput
              icon={<Package className="w-4 h-4" />}
              type="text"
              required
              value={formData.capacity}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, capacity: e.target.value })}
              placeholder="3.5 tons"
            />
          </FormField>

          <FormField label={t('statusLabel')}>
            <IconSelect
              icon={<UserCheck className="w-4 h-4" />}
              value={formData.status}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFormData({ ...formData, status: e.target.value as TruckStatus })}
            >
              <option value="Active">{t('active')}</option>
              <option value="Maintenance">{t('maintenance')}</option>
              <option value="Inactive">{t('inactive')}</option>
            </IconSelect>
          </FormField>

          <FormField label={t('fuelType')}>
            <IconSelect
              icon={<Fuel className="w-4 h-4" />}
              value={formData.fuelType}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFormData({ ...formData, fuelType: e.target.value as 'Diesel' | 'Gasoline' | 'Electric' })}
            >
              <option value="Diesel">{t('fuelTypes.diesel')}</option>
              <option value="Gasoline">{t('fuelTypes.gasoline')}</option>
              <option value="Electric">{t('fuelTypes.electric')}</option>
            </IconSelect>
          </FormField>

          <FormField label={t('mileage')}>
            <IconInput
              icon={<Gauge className="w-4 h-4" />}
              type="number"
              min="0"
              value={formData.mileage}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, mileage: parseInt(e.target.value) })}
            />
          </FormField>

          <FormField label={t('lastMaintenance')}>
            <IconInput
              icon={<Wrench className="w-4 h-4" />}
              type="date"
              value={formData.lastMaintenance}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // Convert date to DD/MM/YYYY format for display
                const newDate = e.target.value;
                setFormData({ ...formData, lastMaintenance: newDate });
              }}
            />
          </FormField>

          <FormField label={t('nextMaintenance')}>
            <IconInput
              icon={<Calendar className="w-4 h-4" />}
              type="date"
              value={formData.nextMaintenance}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // Convert date to DD/MM/YYYY format for display
                const newDate = e.target.value;
                setFormData({ ...formData, nextMaintenance: newDate });
              }}
            />
          </FormField>
        </TwoColumnForm>

        <ModalActions
          onCancel={onClose}
          cancelText={common('cancel')}
          submitText={editingTruck ? common('update') : t('addTruck')}
          icon={<Plus className="w-4 h-4" />}
          showCancel={false}
        />
      </form>
    </CompactModal>
  );
};

// Add/Edit Driver Modal Component
const AddEditDriverModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (driver: Omit<Driver, 'id'>) => void;
  editingDriver?: Driver | null;
}> = ({ isOpen, onClose, onSave, editingDriver }) => {
  const t = useFleetTranslations();
  const common = useCommonTranslations();

  const [formData, setFormData] = useState({
    name: '',
    licenseNumber: '',
    phone: '',
    email: '',
    status: 'Available' as DriverStatus,
    joinDate: new Date().toISOString().split('T')[0],
    licenseExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    experience: 0
  });

  useEffect(() => {
    if (editingDriver) {
      setFormData({
        name: editingDriver.name,
        licenseNumber: editingDriver.licenseNumber,
        phone: editingDriver.phone,
        email: editingDriver.email,
        status: editingDriver.status,
        joinDate: editingDriver.joinDate,
        licenseExpiry: editingDriver.licenseExpiry,
        experience: editingDriver.experience
      });
    } else {
      setFormData({
        name: '',
        licenseNumber: '',
        phone: '',
        email: '',
        status: 'Available',
        joinDate: new Date().toISOString().split('T')[0],
        licenseExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        experience: 0
      });
    }
  }, [editingDriver, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  return (
    <CompactModal
      isOpen={isOpen}
      onClose={onClose}
      title={editingDriver ? t('driverDetailsTable') : t('addNewDriver')}
      maxWidth="max-w-xl"
    >
      <form onSubmit={handleSubmit} className="space-y-3">
        <TwoColumnForm>
          <FormField label={`${t('fullName')} *`}>
            <IconInput
              icon={<Users className="w-4 h-4" />}
              type="text"
              required
              value={formData.name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Ahmed Hassan"
            />
          </FormField>

          <FormField label={`${t('licenseNumber')} *`}>
            <IconInput
              icon={<UserCheck className="w-4 h-4" />}
              type="text"
              required
              value={formData.licenseNumber}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, licenseNumber: e.target.value })}
              placeholder="**********"
            />
          </FormField>

          <FormField label={`${t('phoneNumber')} *`}>
            <IconInput
              icon={<Phone className="w-4 h-4" />}
              type="tel"
              required
              value={formData.phone}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, phone: e.target.value })}
              placeholder="+20-************"
            />
          </FormField>

          <FormField label={`${t('emailAddress')} *`}>
            <IconInput
              icon={<Mail className="w-4 h-4" />}
              type="email"
              required
              value={formData.email}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, email: e.target.value })}
              placeholder="<EMAIL>"
            />
          </FormField>

          <FormField label={t('statusLabel')}>
            <IconSelect
              icon={<UserCheck className="w-4 h-4" />}
              value={formData.status}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setFormData({ ...formData, status: e.target.value as DriverStatus })}
            >
              <option value="Available">{t('available')}</option>
              <option value="On Duty">{t('onDuty')}</option>
              <option value="Off Duty">{t('offDuty')}</option>
              <option value="Inactive">{t('inactive')}</option>
            </IconSelect>
          </FormField>

          <FormField label={`${t('experience')} (${t('yearsExp')})`}>
            <IconInput
              icon={<UserCheck className="w-4 h-4" />}
              type="number"
              min="0"
              max="50"
              value={formData.experience}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData({ ...formData, experience: parseInt(e.target.value) })}
            />
          </FormField>

          <FormField label={t('joinDate')}>
            <IconInput
              icon={<Calendar className="w-4 h-4" />}
              type="date"
              value={formData.joinDate}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // Convert date to DD/MM/YYYY format for display
                const newDate = e.target.value;
                setFormData({ ...formData, joinDate: newDate });
              }}
            />
          </FormField>

          <FormField label={t('licenseExpiry')}>
            <IconInput
              icon={<AlertTriangle className="w-4 h-4" />}
              type="date"
              value={formData.licenseExpiry}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                // Convert date to DD/MM/YYYY format for display
                const newDate = e.target.value;
                setFormData({ ...formData, licenseExpiry: newDate });
              }}
            />
          </FormField>
        </TwoColumnForm>

        <ModalActions
          onCancel={onClose}
          cancelText={common('cancel')}
          submitText={editingDriver ? common('update') : t('addDriver')}
          icon={<Plus className="w-4 h-4" />}
          showCancel={false}
        />
      </form>
    </CompactModal>
  );
};

// Truck Details Modal Component
const TruckDetailsModal: React.FC<{
  truck: Truck | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (truck: Truck) => void;
  onDelete: (truck: Truck) => void;
}> = ({ truck, isOpen, onClose, onEdit, onDelete }) => {
  const locale = useLocale();

  if (!isOpen || !truck) return null;

  const getTruckStatusColor = (status: TruckStatus) => {
    const colors = {
      Active: 'bg-green-100 text-green-800',
      Maintenance: 'bg-yellow-100 text-yellow-800',
      Inactive: 'bg-red-100 text-red-800'
    };
    return colors[status];
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Truck Details</h2>
            <div className="flex items-center gap-2">
              <button
                onClick={() => onEdit(truck)}
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2"
              >
                <Edit2 className="w-4 h-4" />
                Edit
              </button>
              <button
                onClick={() => {
                  onDelete(truck);
                  onClose();
                }}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                Delete
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center">
              <Truck className="w-8 h-8 text-indigo-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">{truck.licensePlate}</h3>
              <p className="text-gray-600">{truck.model} ({truck.year})</p>
              <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getTruckStatusColor(truck.status)}`}>
                {truck.status === 'Active' ? 'نشط' :
                 truck.status === 'Maintenance' ? 'صيانة' :
                 truck.status === 'Inactive' ? 'غير نشط' : truck.status}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Package className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">Capacity: {truck.capacity}</span>
              </div>
              <div className="flex items-center gap-3">
                <Fuel className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">Fuel: {truck.fuelType}</span>
              </div>
              <div className="flex items-center gap-3">
                <Gauge className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">Mileage: {truck.mileage.toLocaleString()} km</span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Wrench className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">Last Maintenance: {new Date(truck.lastMaintenance).toLocaleDateString(locale === 'ar' ? 'ar-EG' : undefined, { 
                  day: '2-digit', 
                  month: '2-digit', 
                  year: 'numeric' 
                })}</span>
              </div>
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">Next Maintenance: {new Date(truck.nextMaintenance).toLocaleDateString(locale === 'ar' ? 'ar-EG' : undefined, { 
                  day: '2-digit', 
                  month: '2-digit', 
                  year: 'numeric' 
                })}</span>
              </div>
              {truck.driverName && (
                <div className="flex items-center gap-3">
                  <Users className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-900">Driver: {truck.driverName}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Driver Details Modal Component
const DriverDetailsModal: React.FC<{
  driver: Driver | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (driver: Driver) => void;
  onDelete: (driver: Driver) => void;
}> = ({ driver, isOpen, onClose, onEdit, onDelete }) => {
  const locale = useLocale();

  if (!isOpen || !driver) return null;

  const getDriverStatusColor = (status: DriverStatus) => {
    const colors = {
      Available: 'bg-green-100 text-green-800',
      'On Duty': 'bg-blue-100 text-blue-800',
      'Off Duty': 'bg-gray-100 text-gray-800',
      Inactive: 'bg-red-100 text-red-800'
    };
    return colors[status];
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Driver Details</h2>
            <div className="flex items-center gap-2">
              <button
                onClick={() => onEdit(driver)}
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2"
              >
                <Edit2 className="w-4 h-4" />
                Edit
              </button>
              <button
                onClick={() => {
                  onDelete(driver);
                  onClose();
                }}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                Delete
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
              {driver.name.charAt(0)}
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">{driver.name}</h3>
              <p className="text-gray-600">License: {driver.licenseNumber}</p>
              <span className={`px-3 py-1 text-sm font-semibold rounded-full ${getDriverStatusColor(driver.status)}`}>
                {driver.status}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">{driver.email}</span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">{driver.phone}</span>
              </div>
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">Experience: {driver.experience} years</span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">Joined: {new Date(driver.joinDate).toLocaleDateString(locale === 'ar' ? 'ar-EG' : undefined, { 
                  day: '2-digit', 
                  month: '2-digit', 
                  year: 'numeric' 
                })}</span>
              </div>
              <div className="flex items-center gap-3">
                <AlertTriangle className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">License Expires: {new Date(driver.licenseExpiry).toLocaleDateString(locale === 'ar' ? 'ar-EG' : undefined, { 
                  day: '2-digit', 
                  month: '2-digit', 
                  year: 'numeric' 
                })}</span>
              </div>
              {driver.truckLicensePlate && (
                <div className="flex items-center gap-3">
                  <Truck className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-900">Assigned Truck: {driver.truckLicensePlate}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Assignment Modal Component
const AssignmentModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onAssign: (assignmentData: { driverId: string; truckId: string }) => void;
  type: 'assign-driver-to-truck' | 'assign-truck-to-driver';
  selectedItem: Truck | Driver | null;
  availableItems: (Truck | Driver)[];
}> = ({ isOpen, onClose, onAssign, type, selectedItem, availableItems }) => {
  const t = useFleetTranslations();
  const [isVisible, setIsVisible] = useState(false);
  const [selectedId, setSelectedId] = useState('');

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => setIsVisible(true), 10);
      setSelectedId('');
    } else {
      setIsVisible(false);
    }
  }, [isOpen]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedId || !selectedItem) return;

    if (type === 'assign-driver-to-truck') {
      onAssign({ driverId: selectedId, truckId: selectedItem.id });
    } else {
      onAssign({ driverId: selectedItem.id, truckId: selectedId });
    }
    handleClose();
  };

  if (!isOpen || !selectedItem) return null;

  const isAssigningDriver = type === 'assign-driver-to-truck';
  const title = isAssigningDriver
    ? `Assign Driver to ${(selectedItem as Truck).licensePlate}`
    : `Assign Truck to ${(selectedItem as Driver).name}`;

  return (
    <div
      className={`fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 transition-opacity duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
      onClick={handleClose}
    >
      <div
        className={`bg-white rounded-2xl p-6 w-full max-w-md transform transition-all duration-300 ease-out ${
          isVisible ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <h3 className="text-xl font-bold mb-4">{title}</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isAssigningDriver ? t('selectDriver') : t('selectTruck')} *
            </label>
            <select
              value={selectedId}
              onChange={(e) => setSelectedId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            >
              <option value="">
                {isAssigningDriver ? t('chooseDriver') : t('chooseTruck')}
              </option>
              {availableItems.map((item) => {
                if (isAssigningDriver) {
                  const driver = item as Driver;
                  return (
                    <option key={driver.id} value={driver.id}>
                      {driver.name} - {driver.status}
                    </option>
                  );
                } else {
                  const truck = item as Truck;
                  return (
                    <option key={truck.id} value={truck.id}>
                      {truck.licensePlate} - {truck.model} ({truck.status})
                    </option>
                  );
                }
              })}
            </select>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!selectedId}
              className="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Assign
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Main Fleet Management Component
export default function FleetPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const t = useFleetTranslations();
  const common = useCommonTranslations();
  const confirmationT = useConfirmationTranslations();
  const toastT = useToastTranslations();
  const { showConfirmation, ConfirmationDialogComponent } = useConfirmationDialog();
  const { success, error } = useToastHelpers();
  const locale = useLocale();

  const [trucks, setTrucks] = useState<Truck[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'trucks' | 'drivers'>('trucks');
  const [showAddTruckModal, setShowAddTruckModal] = useState(false);
  const [showAddDriverModal, setShowAddDriverModal] = useState(false);
  const [editingTruck, setEditingTruck] = useState<Truck | null>(null);
  const [editingDriver, setEditingDriver] = useState<Driver | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTruck, setSelectedTruck] = useState<Truck | null>(null);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [showTruckDetailsModal, setShowTruckDetailsModal] = useState(false);
  const [showDriverDetailsModal, setShowDriverDetailsModal] = useState(false);
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [assignmentType, setAssignmentType] = useState<'assign-driver-to-truck' | 'assign-truck-to-driver'>('assign-driver-to-truck');
  const [assignmentSelectedItem, setAssignmentSelectedItem] = useState<Truck | Driver | null>(null);

  // Navigation state
  const [activeItem, setActiveItem] = useState('fleet');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const [showSettings, setShowSettings] = useState(false);

  // Navigation transition hook
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  // Navigation handler
  const handleNavigation = (path: string) => {
    navigateWithTransition(path);
  };

  const loadFleetData = useCallback(async () => {
    try {
      setLoading(true);
      // Use Firebase service instead of local API
      const { FleetService } = await import('@/services/firebase/fleet');

      const [trucksData, driversData] = await Promise.all([
        FleetService.getAllTrucks(),
        FleetService.getAllDrivers()
      ]);

      // Convert Firebase data to UI format
      const formattedTrucks = trucksData.map(truck => ({
        id: truck.id,
        licensePlate: truck.licensePlate,
        model: truck.model,
        year: truck.year,
        capacity: truck.capacity,
        status: truck.status,
        driverId: truck.driverId,
        driverName: truck.driverName,
        lastMaintenance: truck.lastMaintenance,
        nextMaintenance: truck.nextMaintenance,
        mileage: truck.mileage,
        fuelType: truck.fuelType
      }));

      const formattedDrivers = driversData.map(driver => ({
        id: driver.id,
        name: driver.name,
        licenseNumber: driver.licenseNumber,
        phone: driver.phone,
        email: driver.email,
        status: driver.status,
        truckId: driver.truckId,
        truckLicensePlate: driver.truckLicensePlate,
        joinDate: driver.joinDate,
        licenseExpiry: driver.licenseExpiry,
        experience: driver.experience
      }));

      setTrucks(formattedTrucks);
      setDrivers(formattedDrivers);
    } catch (error) {
      console.error('Error loading fleet data:', error);
      setTrucks([]);
      setDrivers([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    if (user) {
      loadFleetData();
    }
  }, [user, loadFleetData]);

  const handleAddTruck = async (truckData: Omit<Truck, 'id'>) => {
    try {
      // Use Firebase service instead of local API
      const { FleetService } = await import('@/services/firebase/fleet');

      // Convert UI format to Firebase format
      const firebaseTruckData = {
        licensePlate: truckData.licensePlate,
        model: truckData.model,
        year: truckData.year,
        capacity: truckData.capacity,
        status: truckData.status,
        lastMaintenance: truckData.lastMaintenance,
        nextMaintenance: truckData.nextMaintenance,
        mileage: truckData.mileage,
        fuelType: truckData.fuelType,
        ...(truckData.driverId && { driverId: truckData.driverId }),
        ...(truckData.driverName && { driverName: truckData.driverName })
      };

      await FleetService.createTruck(firebaseTruckData);
      await loadFleetData();
    } catch (error) {
      console.error('Error adding truck:', error);
      alert('Failed to add truck');
    }
  };

  const handleAddDriver = async (driverData: Omit<Driver, 'id'>) => {
    try {
      // Use Firebase service instead of local API
      const { FleetService } = await import('@/services/firebase/fleet');

      // Convert UI format to Firebase format
      const firebaseDriverData = {
        name: driverData.name,
        licenseNumber: driverData.licenseNumber,
        phone: driverData.phone,
        email: driverData.email,
        status: driverData.status,
        joinDate: driverData.joinDate,
        licenseExpiry: driverData.licenseExpiry,
        experience: driverData.experience,
        ...(driverData.truckId && { truckId: driverData.truckId }),
        ...(driverData.truckLicensePlate && { truckLicensePlate: driverData.truckLicensePlate })
      };

      await FleetService.createDriver(firebaseDriverData);
      await loadFleetData();
    } catch (error) {
      console.error('Error adding driver:', error);
      alert('Failed to add driver');
    }
  };

  const handleEditTruck = async (truckData: Omit<Truck, 'id'>) => {
    if (!editingTruck) return;
    try {
      // Use Firebase service instead of local API
      const { FleetService } = await import('@/services/firebase/fleet');

      // Convert UI format to Firebase format
      const firebaseUpdates = {
        licensePlate: truckData.licensePlate,
        model: truckData.model,
        year: truckData.year,
        capacity: truckData.capacity,
        status: truckData.status,
        lastMaintenance: truckData.lastMaintenance,
        nextMaintenance: truckData.nextMaintenance,
        mileage: truckData.mileage,
        fuelType: truckData.fuelType,
        ...(truckData.driverId && { driverId: truckData.driverId }),
        ...(truckData.driverName && { driverName: truckData.driverName })
      };

      await FleetService.updateTruck(editingTruck.id, firebaseUpdates);
      await loadFleetData();
      setEditingTruck(null);
    } catch (error) {
      console.error('Error updating truck:', error);
      alert('Failed to update truck');
    }
  };

  const handleEditDriver = async (driverData: Omit<Driver, 'id'>) => {
    if (!editingDriver) return;
    try {
      // Use Firebase service instead of local API
      const { FleetService } = await import('@/services/firebase/fleet');

      // Convert UI format to Firebase format
      const firebaseUpdates = {
        name: driverData.name,
        licenseNumber: driverData.licenseNumber,
        phone: driverData.phone,
        email: driverData.email,
        status: driverData.status,
        joinDate: driverData.joinDate,
        licenseExpiry: driverData.licenseExpiry,
        experience: driverData.experience,
        ...(driverData.truckId && { truckId: driverData.truckId }),
        ...(driverData.truckLicensePlate && { truckLicensePlate: driverData.truckLicensePlate })
      };

      await FleetService.updateDriver(editingDriver.id, firebaseUpdates);
      await loadFleetData();
      setEditingDriver(null);
    } catch (error) {
      console.error('Error updating driver:', error);
      alert('Failed to update driver');
    }
  };

  // Deactivate truck (preserves data integrity)
  const handleDeactivateTruck = async (truck: Truck) => {
    showConfirmation({
      title: confirmationT('deactivateTruck.title'),
      message: confirmationT('deactivateTruck.message'),
      type: 'warning',
      confirmText: common('deactivate'),
      showCancel: false, // Use single button mode
      onConfirm: async () => {
        try {
          // Use Firebase service to deactivate instead of delete
          const { FleetService } = await import('@/services/firebase/fleet');
          await FleetService.deactivateTruck(truck.id);
          await loadFleetData();
          success(toastT('truckDeactivated'));
        } catch (err) {
          console.error('Error deactivating truck:', err);
          error(toastT('operationFailed'));
        }
      }
    });
  };

  // Legacy method for backward compatibility
  const handleDeleteTruck = async (truck: Truck) => {
    return handleDeactivateTruck(truck);
  };

  // Deactivate driver (preserves data integrity)
  const handleDeactivateDriver = async (driver: Driver) => {
    showConfirmation({
      title: confirmationT('deactivateDriver.title'),
      message: confirmationT('deactivateDriver.message'),
      type: 'warning',
      confirmText: common('deactivate'),
      showCancel: false, // Use single button mode
      onConfirm: async () => {
        try {
          // Use Firebase service to deactivate instead of delete
          const { FleetService } = await import('@/services/firebase/fleet');
          await FleetService.deactivateDriver(driver.id);
          await loadFleetData();
          success(toastT('driverDeactivated'));
        } catch (err) {
          console.error('Error deactivating driver:', err);
          error(toastT('operationFailed'));
        }
      }
    });
  };

  // Legacy method for backward compatibility
  const handleDeleteDriver = async (driver: Driver) => {
    return handleDeactivateDriver(driver);
  };

  // Assignment functions
  const handleAssignDriverToTruck = (truck: Truck) => {
    setAssignmentType('assign-driver-to-truck');
    setAssignmentSelectedItem(truck);
    setShowAssignmentModal(true);
  };

  const handleAssignTruckToDriver = (driver: Driver) => {
    setAssignmentType('assign-truck-to-driver');
    setAssignmentSelectedItem(driver);
    setShowAssignmentModal(true);
  };

  const handleUnassignDriver = async (truck: Truck) => {
    if (!confirm(`Are you sure you want to unassign the driver from ${truck.licensePlate}?`)) return;

    try {
      const { FleetService } = await import('@/services/firebase/fleet');

      // Update truck to remove driver
      await FleetService.updateTruck(truck.id, {
        driverId: undefined,
        driverName: undefined
      });

      // Update driver to remove truck assignment
      if (truck.driverId) {
        await FleetService.updateDriver(truck.driverId, {
          truckId: undefined,
          truckLicensePlate: undefined
        });
      }

      await loadFleetData();
    } catch (error) {
      console.error('Error unassigning driver:', error);
      alert('Failed to unassign driver');
    }
  };

  const handleUnassignTruck = async (driver: Driver) => {
    if (!confirm(`Are you sure you want to unassign the truck from ${driver.name}?`)) return;

    try {
      const { FleetService } = await import('@/services/firebase/fleet');

      // Update driver to remove truck
      await FleetService.updateDriver(driver.id, {
        truckId: undefined,
        truckLicensePlate: undefined
      });

      // Update truck to remove driver assignment
      if (driver.truckId) {
        await FleetService.updateTruck(driver.truckId, {
          driverId: undefined,
          driverName: undefined
        });
      }

      await loadFleetData();
    } catch (error) {
      console.error('Error unassigning truck:', error);
      alert('Failed to unassign truck');
    }
  };

  const handleAssignment = async (assignmentData: { driverId: string; truckId: string }) => {
    try {
      const { FleetService } = await import('@/services/firebase/fleet');

      const driver = drivers.find(d => d.id === assignmentData.driverId);
      const truck = trucks.find(t => t.id === assignmentData.truckId);

      if (!driver || !truck) {
        alert('Driver or truck not found');
        return;
      }

      // Update truck with driver info
      await FleetService.updateTruck(assignmentData.truckId, {
        driverId: assignmentData.driverId,
        driverName: driver.name
      });

      // Update driver with truck info
      await FleetService.updateDriver(assignmentData.driverId, {
        truckId: assignmentData.truckId,
        truckLicensePlate: truck.licensePlate
      });

      await loadFleetData();
      setShowAssignmentModal(false);
    } catch (error) {
      console.error('Error creating assignment:', error);
      alert('Failed to create assignment');
    }
  };

  // Filter data based on search term
  const filteredTrucks = trucks.filter(truck =>
    truck.licensePlate.toLowerCase().includes(searchTerm.toLowerCase()) ||
    truck.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (truck.driverName && truck.driverName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const filteredDrivers = drivers.filter(driver =>
    driver.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    driver.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    driver.licenseNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (driver.truckLicensePlate && driver.truckLicensePlate.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Get status color for trucks
  const getTruckStatusColor = (status: TruckStatus) => {
    const colors = {
      Active: 'bg-green-100 text-green-800',
      Maintenance: 'bg-yellow-100 text-yellow-800',
      Inactive: 'bg-red-100 text-red-800'
    };
    return colors[status];
  };

  // Get status color for drivers
  const getDriverStatusColor = (status: DriverStatus) => {
    const colors = {
      Available: 'bg-green-100 text-green-800',
      'On Duty': 'bg-blue-100 text-blue-800',
      'Off Duty': 'bg-gray-100 text-gray-800',
      Inactive: 'bg-red-100 text-red-800'
    };
    return colors[status];
  };

  // Handle truck click
  const handleTruckClick = (truck: Truck) => {
    setSelectedTruck(truck);
    setShowTruckDetailsModal(true);
  };

  // Handle driver click
  const handleDriverClick = (driver: Driver) => {
    setSelectedDriver(driver);
    setShowDriverDetailsModal(true);
  };

  // Handle edit from details modal
  const handleEditTruckFromDetails = (truck: Truck) => {
    setEditingTruck(truck);
    setShowTruckDetailsModal(false);
    setShowAddTruckModal(true);
  };

  const handleEditDriverFromDetails = (driver: Driver) => {
    setEditingDriver(driver);
    setShowDriverDetailsModal(false);
    setShowAddDriverModal(true);
  };

  // Get fleet statistics
  const getFleetStats = () => {
    const stats = {
      totalTrucks: trucks.length,
      activeTrucks: trucks.filter(t => t.status === 'Active').length,
      totalDrivers: drivers.length,
      availableDrivers: drivers.filter(d => d.status === 'Available').length,
      trucksInMaintenance: trucks.filter(t => t.status === 'Maintenance').length,
      driversOnDuty: drivers.filter(d => d.status === 'On Duty').length
    };
    return stats;
  };

  const stats = getFleetStats();

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render fleet page if user is not authenticated
  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <PageTransition transitionKey="fleet-loading" animationType="fade">
        <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50" data-page-content>
          <Sidebar
            activeItem={activeItem}
            setActiveItem={setActiveItem}
            isOpen={sidebarOpen}
            setIsOpen={setSidebarOpen}
            onNavigate={handleNavigation}
            onOpenSettings={() => setShowSettings(true)}
          />
          <div className="lg:ml-64 flex items-center justify-center h-screen">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <p className="text-gray-600">{t('loadingFleetData')}</p>
            </div>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition transitionKey="fleet" animationType="fade">
      <div className="h-screen bg-gray-50 text-gray-900" data-page-content>
        <Sidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isOpen={sidebarOpen}
          setIsOpen={setSidebarOpen}
          onNavigate={handleNavigation}
          onOpenSettings={() => setShowSettings(true)}
        />
        <main className="lg:ml-64 flex flex-col h-full overflow-hidden">
          <Header
            showUserMenu={showUserMenu}
            setShowUserMenu={setShowUserMenu}
            setActiveItem={setActiveItem}
            setShowSettings={setShowSettings}
          />
          <div className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-8 pt-25">
            {/* Creative Add New Fleet Section */}
            <div className="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl p-8 mb-8 overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-4 left-4">
                  <Truck className="w-16 h-16 rotate-12" />
                </div>
                <div className="absolute top-8 right-8">
                  <Users className="w-12 h-12 -rotate-12" />
                </div>
                <div className="absolute bottom-4 left-1/3">
                  <Wrench className="w-10 h-10 rotate-45" />
                </div>
              </div>

              <div className="relative z-10 flex flex-col md:flex-row items-center justify-between">
                <div className="mb-6 md:mb-0 text-white">
                  <h2 className="text-3xl font-bold mb-2">{t('expandYour')}</h2>
                  <p className="text-lg opacity-90 mb-4">{t('addTrucksDrivers')}</p>
                  <div className="flex items-center gap-4 text-sm opacity-80">
                    <div className="flex items-center gap-2">
                      <Truck className="w-4 h-4" />
                      <span>{t('vehicleManagement')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      <span>{t('driverAssignment')}</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={() => setShowAddTruckModal(true)}
                    className="bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/30 transition-all duration-200 flex items-center gap-2 font-medium"
                  >
                    <Plus className="w-5 h-5" />
                    {t('addTruck')}
                  </button>
                  <button
                    onClick={() => setShowAddDriverModal(true)}
                    className="bg-white text-indigo-600 px-6 py-3 rounded-xl hover:bg-gray-100 transition-all duration-200 flex items-center gap-2 font-medium shadow-lg"
                  >
                    <Plus className="w-5 h-5" />
                    {t('addDriver')}
                  </button>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('totalTrucks')}</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {loading ? '...' : stats.totalTrucks}
                    </p>
                  </div>
                  <Truck className="w-8 h-8 text-indigo-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('activeTrucks')}</p>
                    <p className="text-3xl font-bold text-green-600">
                      {loading ? '...' : stats.activeTrucks}
                    </p>
                  </div>
                  <UserCheck className="w-8 h-8 text-green-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('totalDrivers')}</p>
                    <p className="text-3xl font-bold text-blue-600">
                      {loading ? '...' : stats.totalDrivers}
                    </p>
                  </div>
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('availableDrivers')}</p>
                    <p className="text-3xl font-bold text-purple-600">
                      {loading ? '...' : stats.availableDrivers}
                    </p>
                  </div>
                  <UserCheck className="w-8 h-8 text-purple-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('inMaintenance')}</p>
                    <p className="text-3xl font-bold text-yellow-600">
                      {loading ? '...' : stats.trucksInMaintenance}
                    </p>
                  </div>
                  <Wrench className="w-8 h-8 text-yellow-600" />
                </div>
              </div>
            </div>

            {/* Fleet Table */}
            <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
              {/* Header */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Truck className="w-6 h-6 text-gray-600" />
                    <h3 className="text-lg font-semibold text-gray-900">{t('title')}</h3>
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
                      {selectedTab === 'trucks' ? filteredTrucks.length : filteredDrivers.length}
                    </span>
                    <span className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-md">
                      {t('clickRowToEdit')}
                    </span>
                  </div>

                  {/* Tab Switcher and Search */}
                  <div className="flex items-center gap-4">
                    <div className="flex bg-gray-100 rounded-lg p-1">
                      <button
                        onClick={() => setSelectedTab('trucks')}
                        className={`px-4 py-2 rounded-md font-medium text-sm transition duration-200 ${
                          selectedTab === 'trucks'
                            ? 'bg-white text-indigo-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        {t('trucks')} ({trucks.length})
                      </button>
                      <button
                        onClick={() => setSelectedTab('drivers')}
                        className={`px-4 py-2 rounded-md font-medium text-sm transition duration-200 ${
                          selectedTab === 'drivers'
                            ? 'bg-white text-indigo-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        {t('drivers')} ({drivers.length})
                      </button>
                    </div>

                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="text"
                        placeholder={`${common('search')} ${selectedTab === 'trucks' ? t('trucks') : t('drivers')}...`}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Table Content */}
              <div className="overflow-x-auto">
                {selectedTab === 'trucks' ? (
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('truckDetailsTable')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('statusLabel')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('driver')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('maintenance')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('action')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredTrucks.length === 0 ? (
                        <tr>
                          <td colSpan={5} className="px-6 py-12 text-center text-gray-500">
                            <Truck className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                            <p className="text-lg font-medium mb-2">{t('noTrucksFound')}</p>
                            <p className="text-sm">{t('addFirstTruck')}</p>
                          </td>
                        </tr>
                      ) : (
                        filteredTrucks.map((truck) => {
                          const handleRowClick = (e: React.MouseEvent) => {
                            // Don't trigger row click if action buttons were clicked
                            if ((e.target as HTMLElement).closest('.action-buttons')) {
                              return;
                            }
                            handleTruckClick(truck);
                          };

                          return (
                            <tr
                              key={truck.id}
                              className="border-b border-gray-100 hover:bg-gray-50 transition-colors group cursor-pointer"
                              onClick={handleRowClick}
                            >
                            <td className="px-6 py-4">
                              <div>
                                <div className="flex items-center gap-3">
                                  <div className="bg-indigo-100 p-2 rounded-lg">
                                    <Truck className="w-5 h-5 text-indigo-600" />
                                  </div>
                                  <div>
                                    <p className="font-medium text-gray-900">{truck.licensePlate}</p>
                                    <p className="text-sm text-gray-500">{truck.model} ({truck.year})</p>
                                    <div className="flex items-center gap-4 mt-1">
                                      <span className="text-xs text-gray-500 flex items-center gap-1">
                                        <Package className="w-3 h-3" />
                                        {truck.capacity}
                                      </span>
                                      <span className="text-xs text-gray-500 flex items-center gap-1">
                                        <Fuel className="w-3 h-3" />
                                        {truck.fuelType}
                                      </span>
                                      <span className="text-xs text-gray-500 flex items-center gap-1">
                                        <Gauge className="w-3 h-3" />
                                        {truck.mileage.toLocaleString()} km
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getTruckStatusColor(truck.status)}`}>
                                {truck.status === 'Active' ? t('active') :
                                 truck.status === 'Maintenance' ? t('maintenance') :
                                 truck.status === 'Inactive' ? t('inactive') : truck.status}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              {truck.driverName ? (
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center">
                                    <span className="text-white text-xs font-medium">
                                      {truck.driverName.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                  <span className="text-sm font-medium text-gray-900">{truck.driverName}</span>
                                </div>
                              ) : (
                                <span className="text-sm text-gray-500">{t('notAssigned')}</span>
                              )}
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm">
                                <p className="text-gray-900">{t('next')}: {new Date(truck.nextMaintenance).toLocaleDateString()}</p>
                                <p className="text-gray-500">{t('last')}: {new Date(truck.lastMaintenance).toLocaleDateString()}</p>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="action-buttons flex items-center gap-2">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleTruckClick(truck);
                                  }}
                                  className="text-indigo-600 hover:text-indigo-800 transition-colors"
                                  title="View Details"
                                >
                                  <Eye className="w-4 h-4" />
                                </button>
                                {truck.driverName ? (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleUnassignDriver(truck);
                                    }}
                                    className="text-orange-600 hover:text-orange-800 transition-colors"
                                    title="Unassign Driver"
                                  >
                                    <Unlink className="w-4 h-4" />
                                  </button>
                                ) : (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleAssignDriverToTruck(truck);
                                    }}
                                    className="text-green-600 hover:text-green-800 transition-colors"
                                    title="Assign Driver"
                                  >
                                    <Link className="w-4 h-4" />
                                  </button>
                                )}
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setEditingTruck(truck);
                                  }}
                                  className="text-blue-600 hover:text-blue-800 transition-colors"
                                  title="Edit truck"
                                >
                                  <Edit2 className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeactivateTruck(truck);
                                  }}
                                  className="text-orange-600 hover:text-orange-800 transition-colors"
                                  title="Deactivate truck"
                                >
                                  <UserX className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                          );
                        })
                      )}
                    </tbody>
                  </table>
                ) : (
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('driverDetailsTable')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('statusLabel')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('contact')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('assignedTruck')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t('action')}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredDrivers.length === 0 ? (
                        <tr>
                          <td colSpan={5} className="px-6 py-12 text-center text-gray-500">
                            <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                            <p className="text-lg font-medium mb-2">{t('noDriversFound')}</p>
                            <p className="text-sm">{t('addFirstDriver')}</p>
                          </td>
                        </tr>
                      ) : (
                        filteredDrivers.map((driver) => {
                          const handleRowClick = (e: React.MouseEvent) => {
                            // Don't trigger row click if action buttons were clicked
                            if ((e.target as HTMLElement).closest('.action-buttons')) {
                              return;
                            }
                            handleDriverClick(driver);
                          };

                          return (
                            <tr
                              key={driver.id}
                              className="border-b border-gray-100 hover:bg-gray-50 transition-colors group cursor-pointer"
                              onClick={handleRowClick}
                            >
                            <td className="px-6 py-4">
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center">
                                  <span className="text-white text-sm font-medium">
                                    {driver.name.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">{driver.name}</p>
                                  <p className="text-sm text-gray-500">{t('license')}: {driver.licenseNumber}</p>
                                  <div className="flex items-center gap-4 mt-1">
                                    <span className="text-xs text-gray-500 flex items-center gap-1">
                                      <Calendar className="w-3 h-3" />
                                      {driver.experience} {t('yearsExp')}
                                    </span>
                                    <span className="text-xs text-gray-500">
                                      {t('joined')}: {new Date(driver.joinDate).toLocaleDateString(locale === 'ar' ? 'ar-EG' : undefined, { 
                                        day: '2-digit', 
                                        month: '2-digit', 
                                        year: 'numeric' 
                                      })}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getDriverStatusColor(driver.status)}`}>
                                {driver.status === 'Available' ? t('available') :
                                 driver.status === 'On Duty' ? t('onDuty') :
                                 driver.status === 'Off Duty' ? t('offDuty') :
                                 driver.status === 'Inactive' ? t('inactive') : driver.status}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm">
                                <div className="flex items-center gap-2 mb-1">
                                  <Phone className="w-3 h-3 text-gray-400" />
                                  <span className="text-gray-900">{driver.phone}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Mail className="w-3 h-3 text-gray-400" />
                                  <span className="text-gray-500">{driver.email}</span>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              {driver.truckLicensePlate ? (
                                <div className="flex items-center gap-2">
                                  <Truck className="w-4 h-4 text-indigo-600" />
                                  <span className="text-sm font-medium text-gray-900">{driver.truckLicensePlate}</span>
                                </div>
                              ) : (
                                <span className="text-sm text-gray-500">{t('notAssigned')}</span>
                              )}
                            </td>
                            <td className="px-6 py-4">
                              <div className="action-buttons flex items-center gap-2">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDriverClick(driver);
                                  }}
                                  className="text-indigo-600 hover:text-indigo-800 transition-colors"
                                  title="View Details"
                                >
                                  <Eye className="w-4 h-4" />
                                </button>
                                {driver.truckLicensePlate ? (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleUnassignTruck(driver);
                                    }}
                                    className="text-orange-600 hover:text-orange-800 transition-colors"
                                    title="Unassign Truck"
                                  >
                                    <Unlink className="w-4 h-4" />
                                  </button>
                                ) : (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleAssignTruckToDriver(driver);
                                    }}
                                    className="text-green-600 hover:text-green-800 transition-colors"
                                    title="Assign Truck"
                                  >
                                    <Link className="w-4 h-4" />
                                  </button>
                                )}
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setEditingDriver(driver);
                                  }}
                                  className="text-blue-600 hover:text-blue-800 transition-colors"
                                  title="Edit driver"
                                >
                                  <Edit2 className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeactivateDriver(driver);
                                  }}
                                  className="text-orange-600 hover:text-orange-800 transition-colors"
                                  title="Deactivate driver"
                                >
                                  <UserX className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                          );
                        })
                      )}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>
        </main>

        {/* Truck Details Modal */}
        <TruckDetailsModal
          truck={selectedTruck}
          isOpen={showTruckDetailsModal}
          onClose={() => {
            setShowTruckDetailsModal(false);
            setSelectedTruck(null);
          }}
          onEdit={handleEditTruckFromDetails}
          onDelete={handleDeleteTruck}
        />

        {/* Driver Details Modal */}
        <DriverDetailsModal
          driver={selectedDriver}
          isOpen={showDriverDetailsModal}
          onClose={() => {
            setShowDriverDetailsModal(false);
            setSelectedDriver(null);
          }}
          onEdit={handleEditDriverFromDetails}
          onDelete={handleDeleteDriver}
        />

        {/* Add/Edit Modals */}
        <AddEditTruckModal
          isOpen={showAddTruckModal || !!editingTruck}
          onClose={() => {
            setShowAddTruckModal(false);
            setEditingTruck(null);
          }}
          onSave={editingTruck ? handleEditTruck : handleAddTruck}
          editingTruck={editingTruck}
        />

        <AddEditDriverModal
          isOpen={showAddDriverModal || !!editingDriver}
          onClose={() => {
            setShowAddDriverModal(false);
            setEditingDriver(null);
          }}
          onSave={editingDriver ? handleEditDriver : handleAddDriver}
          editingDriver={editingDriver}
        />

        {/* Assignment Modal */}
        <AssignmentModal
          isOpen={showAssignmentModal}
          onClose={() => setShowAssignmentModal(false)}
          onAssign={handleAssignment}
          type={assignmentType}
          selectedItem={assignmentSelectedItem}
          availableItems={
            assignmentType === 'assign-driver-to-truck'
              ? drivers.filter(driver => !driver.truckId && driver.status !== 'Inactive')
              : trucks.filter(truck => !truck.driverId && truck.status === 'Active')
          }
        />

        {/* Settings Modal */}
        <SettingsModal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />

        {/* Confirmation Dialog */}
        <ConfirmationDialogComponent />
      </div>
    </PageTransition>
  );
}
'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

export default function RegisterPage() {
  const router = useRouter();
  const { signUp, user, signInWithGoogle } = useAuth();

  // Form state
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [errors, setErrors] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    terms: '',
    general: ''
  });
  const [touched, setTouched] = useState({
    fullName: false,
    email: false,
    password: false,
    confirmPassword: false,
    terms: false
  });
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  // Track if component has mounted to prevent hydration mismatch
  const [isMounted, setIsMounted] = useState(false);

  // Track mounted state to prevent hydration mismatch
  React.useEffect(() => {
    setIsMounted(true);
  }, []);

  // Redirect if user is already logged in (but only after mounting to prevent issues)
  // Add a small delay to prevent immediate redirects when navigating from login page
  React.useEffect(() => {
    if (isMounted && user && !isLoading && !isNavigating) {
      // Add a small delay to prevent flickering when navigating from login
      const timer = setTimeout(() => {
        // Extract locale from current path
        const currentLocale = window.location.pathname.split('/')[1] || 'en';
        router.push(`/${currentLocale}/dashboard`);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [user, router, isMounted, isLoading, isNavigating]);

  // Reset navigation state on component unmount
  React.useEffect(() => {
    return () => {
      setIsNavigating(false);
    };
  }, []);

  const handleFieldBlur = (field: 'fullName' | 'email' | 'password' | 'confirmPassword' | 'terms') => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  const shouldShowError = (field: 'fullName' | 'email' | 'password' | 'confirmPassword' | 'terms') => {
    return !isNavigating && (hasSubmitted || touched[field]);
  };

  const handleNavigation = (path: string) => {
    setIsNavigating(true);
    // Clear validation state before navigation
    setErrors({ fullName: '', email: '', password: '', confirmPassword: '', terms: '', general: '' });
    setTouched({ fullName: false, email: false, password: false, confirmPassword: false, terms: false });
    setHasSubmitted(false);

    // Clear form fields when navigating to login to prevent confusion
    if (path === '/login') {
      setFullName('');
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      setAgreeToTerms(false);
      setShowPassword(false);
      setShowConfirmPassword(false);
    }

    // Extract locale from current path and add to navigation path
    const currentLocale = window.location.pathname.split('/')[1] || 'en';
    const localeAwarePath = path.startsWith('/') ? `/${currentLocale}${path}` : `/${currentLocale}/${path}`;
    router.push(localeAwarePath);
  };

  const validateForm = () => {
    const newErrors = { fullName: '', email: '', password: '', confirmPassword: '', terms: '', general: '' };
    let isValid = true;

    if (!fullName.trim()) {
      newErrors.fullName = 'Full name is required';
      isValid = false;
    }

    if (!email) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
      isValid = false;
    }

    if (!password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
      isValid = false;
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
      isValid = false;
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }

    if (!agreeToTerms) {
      newErrors.terms = 'You must agree to the terms and conditions';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setHasSubmitted(true);

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({ fullName: '', email: '', password: '', confirmPassword: '', terms: '', general: '' });

    try {
      await signUp(email, password, fullName);
      // Extract locale from current path
      const currentLocale = window.location.pathname.split('/')[1] || 'en';
      router.push(`/${currentLocale}/dashboard`);
    } catch (error: unknown) {
      console.error('Registration error:', error);
      let errorMessage = 'Registration failed. Please try again.';

      if (error && typeof error === 'object' && 'code' in error) {
        const firebaseError = error as { code: string };
        if (firebaseError.code === 'auth/email-already-in-use') {
          errorMessage = 'An account with this email already exists.';
        } else if (firebaseError.code === 'auth/weak-password') {
          errorMessage = 'Password is too weak. Please choose a stronger password.';
        } else if (firebaseError.code === 'auth/invalid-email') {
          errorMessage = 'Invalid email address.';
        }
      }

      setErrors({ fullName: '', email: '', password: '', confirmPassword: '', terms: '', general: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setIsGoogleLoading(true);
    setErrors({ fullName: '', email: '', password: '', confirmPassword: '', terms: '', general: '' });

    try {
      await signInWithGoogle();
      // Extract locale from current path
      const currentLocale = window.location.pathname.split('/')[1] || 'en';
      router.push(`/${currentLocale}/dashboard`);
    } catch (error: unknown) {
      console.error('Google sign up error:', error);
      setErrors({ fullName: '', email: '', password: '', confirmPassword: '', terms: '', general: 'Google sign up failed. Please try again.' });
    } finally {
      setIsGoogleLoading(false);
    }
  };

  // Don't render anything until mounted to prevent hydration mismatch
  if (!isMounted) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-blue-100 p-4 relative overflow-hidden animate-fadeIn">
      {/* Background cloud decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <svg className="absolute top-0 right-0 w-96 h-96 opacity-10" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M100 200C100 155.817 135.817 120 180 120C224.183 120 260 155.817 260 200C304.183 200 340 235.817 340 280C340 324.183 304.183 360 260 360H140C95.8172 360 60 324.183 60 280C60 235.817 95.8172 200 140 200H100Z" fill="url(#gradient1)"/>
          <defs>
            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.1"/>
              <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.1"/>
            </linearGradient>
          </defs>
        </svg>

        <svg className="absolute bottom-0 left-0 w-80 h-80 opacity-10" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M200 100C244.183 100 280 135.817 280 180C280 224.183 244.183 260 200 260C155.817 260 120 224.183 120 180C120 135.817 155.817 100 200 100Z" fill="url(#gradient2)"/>
          <defs>
            <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.1"/>
              <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.1"/>
            </linearGradient>
          </defs>
        </svg>
      </div>

      <div className="w-full max-w-6xl overflow-hidden bg-white rounded-3xl shadow-xl flex relative z-10">
        {/* Left side - Register form */}
        <div className="w-full md:w-1/2 p-8 md:p-12 relative z-10 animate-slideInLeft">
          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold mb-1">Create Account!</h1>
              <p className="text-gray-600">Join Zawaya Delivery today</p>
            </div>

            <form onSubmit={handleSubmit}>
              {/* Full Name Input */}
              <div className="mb-6">
                <div className="flex items-center bg-gray-50 rounded-3xl shadow-md overflow-hidden border border-gray-100 focus-within:border-purple-300 focus-within:shadow-lg transition-all duration-200">
                  <div className="pl-4">
                    <div className="w-9 h-9 flex items-center justify-center bg-gradient-to-br from-purple-500 to-purple-700 rounded-full shadow-lg shadow-purple-300 border border-white/50">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <circle cx="12" cy="7" r="4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <input
                    type="text"
                    placeholder="Full Name"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    onBlur={() => handleFieldBlur('fullName')}
                    className="flex-1 px-4 py-4 bg-transparent text-gray-700 placeholder-gray-400 focus:outline-none"
                  />
                </div>
                {shouldShowError('fullName') && errors.fullName && <p className="text-red-500 text-sm mt-1 ml-4">{errors.fullName}</p>}
              </div>

              {/* Email Input */}
              <div className="mb-6">
                <div className="flex items-center bg-gray-50 rounded-3xl shadow-md overflow-hidden border border-gray-100 focus-within:border-purple-300 focus-within:shadow-lg transition-all duration-200">
                  <div className="pl-4">
                    <div className="w-9 h-9 flex items-center justify-center bg-gradient-to-br from-purple-500 to-purple-700 rounded-full shadow-lg shadow-purple-300 border border-white/50">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 4H4C2.9 4 2 4.9 2 6V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <polyline points="22,6 12,13 2,6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <input
                    type="email"
                    placeholder="Email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    onBlur={() => handleFieldBlur('email')}
                    className="flex-1 px-4 py-4 bg-transparent text-gray-700 placeholder-gray-400 focus:outline-none"
                  />
                </div>
                {shouldShowError('email') && errors.email && <p className="text-red-500 text-sm mt-1 ml-4">{errors.email}</p>}
              </div>

              {/* Password Input */}
              <div className="mb-6">
                <div className="flex items-center bg-gray-50 rounded-3xl shadow-md overflow-hidden border border-gray-100 focus-within:border-purple-300 focus-within:shadow-lg transition-all duration-200">
                  <div className="pl-4">
                    <div className="w-9 h-9 flex items-center justify-center bg-gradient-to-br from-purple-500 to-purple-700 rounded-full shadow-lg shadow-purple-300 border border-white/50">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="white" strokeWidth="2"/>
                        <circle cx="12" cy="16" r="1" fill="white"/>
                        <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    placeholder="Password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    onBlur={() => handleFieldBlur('password')}
                    className="flex-1 px-4 py-4 bg-transparent text-gray-700 placeholder-gray-400 focus:outline-none"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="pr-4 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      {showPassword ? (
                        <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.231 7.81663 6.62 6.62M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      ) : (
                        <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      )}
                      <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      {showPassword && <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>}
                    </svg>
                  </button>
                </div>
                {shouldShowError('password') && errors.password && <p className="text-red-500 text-sm mt-1 ml-4">{errors.password}</p>}
              </div>

              {/* Confirm Password Input */}
              <div className="mb-6">
                <div className="flex items-center bg-gray-50 rounded-3xl shadow-md overflow-hidden border border-gray-100 focus-within:border-purple-300 focus-within:shadow-lg transition-all duration-200">
                  <div className="pl-4">
                    <div className="w-9 h-9 flex items-center justify-center bg-gradient-to-br from-purple-500 to-purple-700 rounded-full shadow-lg shadow-purple-300 border border-white/50">
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="white" strokeWidth="2"/>
                        <circle cx="12" cy="16" r="1" fill="white"/>
                        <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm Password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    onBlur={() => handleFieldBlur('confirmPassword')}
                    className="flex-1 px-4 py-4 bg-transparent text-gray-700 placeholder-gray-400 focus:outline-none"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="pr-4 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      {showConfirmPassword ? (
                        <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.231 7.81663 6.62 6.62M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5717 9.14351 13.1984C8.99262 12.8251 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      ) : (
                        <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      )}
                      <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      {showConfirmPassword && <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>}
                    </svg>
                  </button>
                </div>
                {shouldShowError('confirmPassword') && errors.confirmPassword && <p className="text-red-500 text-sm mt-1 ml-4">{errors.confirmPassword}</p>}
              </div>

              {/* Terms and Conditions */}
              <div className="flex items-center mb-6">
                <input
                  type="checkbox"
                  id="terms"
                  className="h-4 w-4 rounded-full"
                  checked={agreeToTerms}
                  onChange={() => setAgreeToTerms(!agreeToTerms)}
                  onBlur={() => handleFieldBlur('terms')}
                />
                <label htmlFor="terms" className="ml-2 text-sm text-gray-500">
                  I agree to the <a href="#" className="text-purple-600 hover:underline">Terms and Conditions</a> and <a href="#" className="text-purple-600 hover:underline">Privacy Policy</a>
                </label>
              </div>
              {shouldShowError('terms') && errors.terms && <p className="text-red-500 text-sm mb-4">{errors.terms}</p>}

              {/* Error Messages */}
              {errors.general && (
                <div className="mb-4 text-center">
                  <p className="text-red-500">{errors.general}</p>
                </div>
              )}

              {/* Sign Up Button */}
              <div className="flex justify-center">
                <button
                  type="submit"
                  className="bg-gradient-to-r from-purple-500 to-purple-600 text-white py-3 px-12 rounded-full font-semibold text-sm tracking-wide hover:from-purple-600 hover:to-purple-700 transition duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
                  disabled={isLoading || isGoogleLoading}
                >
                  {isLoading ? 'Creating Account...' : 'CREATE ACCOUNT'}
                </button>
              </div>

              {/* Divider */}
              <div className="flex items-center my-6">
                <div className="flex-1 border-t border-gray-200"></div>
                <span className="px-4 text-gray-500 text-sm">or</span>
                <div className="flex-1 border-t border-gray-200"></div>
              </div>

              {/* Google Sign Up */}
              <div className="flex justify-center">
                <button
                  type="button"
                  onClick={handleGoogleSignUp}
                  disabled={isLoading || isGoogleLoading}
                  className="flex items-center justify-center gap-3 bg-white border border-gray-300 text-gray-700 py-3 px-8 rounded-full font-semibold text-sm tracking-wide hover:bg-gray-50 transition duration-300 shadow-md hover:shadow-lg transform hover:scale-[1.02] disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isGoogleLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-700"></div>
                      <span>Signing up...</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                      </svg>
                      <span>Continue with Google</span>
                    </>
                  )}
                </button>
              </div>

              {/* Sign In Link */}
              <div className="text-center mt-6">
                <p className="text-gray-600 text-sm">
                  Already have an account? <button onClick={() => handleNavigation('/login')} className="text-purple-600 hover:underline">Sign In</button>
                </p>
              </div>
            </form>
          </div>
        </div>

        {/* SVG Definitions */}
        <svg className="absolute" width="0" height="0">
          <defs>
            <clipPath id="cloud-border" clipPathUnits="objectBoundingBox">
              <path d="M0.5,0 C0.65,0.05 0.75,0.15 1,0 V1 H0 V0 C0.2,0.15 0.35,0.05 0.5,0" />
            </clipPath>
          </defs>
        </svg>

        {/* Wave divider */}
        <div className="absolute top-0 bottom-0 left-1/2 w-full h-full overflow-hidden pointer-events-none wave-divider">
          <svg className="absolute h-full" viewBox="0 0 100 100" preserveAspectRatio="none" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0,0 C20,5 35,15 50,0 C65,15 80,5 100,0 L100,100 L0,100 Z" fill="white"/>
          </svg>
        </div>

        {/* Right side - Welcome section with purple gradient and cloud border */}
        <div className="hidden md:block md:w-1/2 bg-gradient-to-br from-purple-500 to-purple-700 relative overflow-hidden animate-slideInRight">
          {/* Enhanced Cloud overlays */}
          <div className="absolute inset-0">
            {/* Top cloud border */}
            <svg className="absolute top-0 left-0 w-full h-40" viewBox="0 0 400 120" preserveAspectRatio="none" fill="white" xmlns="http://www.w3.org/2000/svg">
              <path d="M0,0 C40,40 80,20 120,35 C160,50 200,25 240,15 C280,5 320,25 360,35 C380,40 390,35 400,30 L400,120 L0,120 Z" opacity="0.3"/>
            </svg>

            {/* Bottom cloud border */}
            <svg className="absolute bottom-0 left-0 w-full h-40" viewBox="0 0 400 120" preserveAspectRatio="none" fill="white" xmlns="http://www.w3.org/2000/svg">
              <path d="M0,120 C40,80 80,100 120,85 C160,70 200,95 240,105 C280,115 320,95 360,85 C380,80 390,85 400,90 L400,0 L0,0 Z" opacity="0.3"/>
            </svg>

            {/* Additional decorative clouds */}
            <svg className="absolute top-1/4 right-8 w-32 h-32 opacity-20" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M50 100C50 77.909 67.909 60 90 60C112.091 60 130 77.909 130 100C152.091 100 170 117.909 170 140C170 162.091 152.091 180 130 180H70C47.909 180 30 162.091 30 140C30 117.909 47.909 100 70 100H50Z" fill="white"/>
            </svg>

            <svg className="absolute bottom-1/4 left-8 w-24 h-24 opacity-15" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M60 120C60 97.909 77.909 80 100 80C122.091 80 140 97.909 140 120C162.091 120 180 137.909 180 160C180 182.091 162.091 200 140 200H80C57.909 200 40 182.091 40 160C40 137.909 57.909 120 80 120H60Z" fill="white"/>
            </svg>
          </div>

          {/* Content */}
          <div className="relative z-10 p-12 text-white flex flex-col justify-center h-full">
            <div className="max-w-md mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4">Join Zawaya!</h2>
              <p className="mb-6 text-white/80 leading-relaxed">
                Start your journey with Zawaya Delivery and transform your logistics operations. Get access to powerful tools for managing orders, tracking deliveries, and optimizing your delivery network with real-time analytics and insights.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Loading Overlay */}
      {isNavigating && (
        <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4"></div>
            <p className="text-gray-600 font-medium">Loading...</p>
          </div>
        </div>
      )}
    </div>
  );
}

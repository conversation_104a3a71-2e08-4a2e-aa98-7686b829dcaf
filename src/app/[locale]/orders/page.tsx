'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useOrdersTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import { ResponsiveTable, ResponsiveTableRow, ResponsiveTableCell } from '@/components/ResponsiveTable';
import {
  Package,
  Truck,
  Users,
  Search,
  X,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  Edit,
  Phone,
  ArrowRight,
  Calendar,
  ChevronDown,
  ChevronUp,
  ClipboardList,
  Play,
  Building2,
  Car,
  Home,
  Gift,
  DollarSign,
  Plus
} from 'lucide-react';

import { SettingsModal } from '@/components/SettingsModal';
import { PageTransition } from '@/components/PageTransition';
import { useNavigationTransition } from '@/hooks/useNavigationTransition';
import { Sidebar } from '@/components/Sidebar';
import { Header } from '@/components/Header';

import { 
  CompactModal, 
  FormField, 
  IconInput, 
  IconSelect, 
  ModalActions, 
  TwoColumnForm, 
  FullWidthField 
} from '@/components/CompactModal';

// Types
type OrderStatus =
  | 'Pending'
  | 'Started'
  | 'Moved to Supplier'
  | 'Arrived at Supplier'
  | 'Moving to Customer'
  | 'Arrived at Customer'
  | 'Delivered'
  | 'Cancelled';

type ActionLog = {
  id: string;
  timestamp: string;
  status: OrderStatus;
  location: string;
  gpsCoordinates?: { lat: number; lng: number };
  userId: string;
  userName: string;
  notes?: string;
};

type PaymentInfo = {
  supplierPayment?: {
    amount: string;
    timestamp: string;
    confirmedBy: string;
  };
  customerPayment?: {
    amount: string;
    method: 'Cash' | 'Card' | 'Digital' | 'Other';
    deliveryStatus: 'Full' | 'Partial';
    timestamp: string;
    receivedBy: string;
  };
};

type Order = {
  id: string;
  customer: string;
  avatar: string | null;
  status: OrderStatus;
  date: string;
  amount: string;
  address?: string;
  phone?: string;
  orderType?: string;
  items?: Array<{ name: string; quantity: number; price: string }>;
  actionLog?: ActionLog[];
  paymentInfo?: PaymentInfo;
  scheduledDate?: string;
  salesRep?: string;
  estimatedDeliveryTime?: string;
  depositAmount?: string;
  deliveryAgent?: string;
  cancellationReason?: string;
  purchasingRep?: string;
  driverId?: string;
  driverName?: string;
  truckId?: string;
  truckLicensePlate?: string;
  totalOrderAmount?: string;
};

type TabType = 'oncoming' | 'ongoing' | 'completed' | 'cancelled';



// Status flow progression
const statusFlow: Record<OrderStatus, OrderStatus | null> = {
  'Pending': 'Started',
  'Started': 'Moved to Supplier',
  'Moved to Supplier': 'Arrived at Supplier',
  'Arrived at Supplier': 'Moving to Customer',
  'Moving to Customer': 'Arrived at Customer',
  'Arrived at Customer': 'Delivered',
  'Delivered': null,
  'Cancelled': null
};

// Status descriptions
const statusDescriptions: Record<OrderStatus, string> = {
  'Pending': 'Order received and waiting to be started',
  'Started': 'Order processing has begun',
  'Moved to Supplier': 'Order is being moved to supplier location',
  'Arrived at Supplier': 'Order has arrived at supplier location',
  'Moving to Customer': 'Order is being delivered to customer',
  'Arrived at Customer': 'Order has arrived at customer location',
  'Delivered': 'Order has been successfully delivered',
  'Cancelled': 'Order has been cancelled'
};



const getStatusClass = (status: OrderStatus) => {
  switch (status) {
    case 'Pending':
      return 'bg-blue-100 text-blue-800';
    case 'Started':
      return 'bg-indigo-100 text-indigo-800';
    case 'Moved to Supplier':
      return 'bg-purple-100 text-purple-800';
    case 'Arrived at Supplier':
      return 'bg-yellow-100 text-yellow-800';
    case 'Moving to Customer':
      return 'bg-orange-100 text-orange-800';
    case 'Arrived at Customer':
      return 'bg-teal-100 text-teal-800';
    case 'Delivered':
      return 'bg-green-100 text-green-800';
    case 'Cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Helper function to get status index for timeline progression
const getStatusIndex = (status: OrderStatus): number => {
  const statusOrder: OrderStatus[] = [
    'Pending',
    'Started',
    'Moved to Supplier',
    'Arrived at Supplier',
    'Moving to Customer',
    'Arrived at Customer',
    'Delivered'
  ];
  return statusOrder.indexOf(status);
};

// Helper function to get contextual icon for each status
const getStatusIcon = (status: OrderStatus) => {
  const getIconColor = () => {
    switch (status) {
      case 'Pending':
        return 'text-amber-600';
      case 'Started':
        return 'text-emerald-600';
      case 'Moved to Supplier':
        return 'text-sky-600';
      case 'Arrived at Supplier':
        return 'text-violet-600';
      case 'Moving to Customer':
        return 'text-rose-600';
      case 'Arrived at Customer':
        return 'text-pink-600';
      case 'Delivered':
        return 'text-yellow-600';
      default:
        return 'text-slate-600';
    }
  };

  const iconColor = getIconColor();

  switch (status) {
    case 'Pending':
      return (
        <div className="relative">
          <ClipboardList className={`w-6 h-6 ${iconColor}`} />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-amber-400 rounded-full animate-ping"></div>
        </div>
      );
    case 'Started':
      return (
        <div className="relative">
          <Play className={`w-6 h-6 ${iconColor}`} />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full animate-ping"></div>
        </div>
      );
    case 'Moved to Supplier':
      return (
        <div className="relative">
          <Truck className={`w-6 h-6 ${iconColor}`} />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-sky-400 rounded-full animate-ping"></div>
        </div>
      );
    case 'Arrived at Supplier':
      return <Building2 className={`w-6 h-6 ${iconColor}`} />;
    case 'Moving to Customer':
      return (
        <div className="relative">
          <Car className={`w-6 h-6 ${iconColor}`} />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-rose-400 rounded-full animate-ping"></div>
        </div>
      );
    case 'Arrived at Customer':
      return <Home className={`w-6 h-6 ${iconColor}`} />;
    case 'Delivered':
      return (
        <div className="relative">
          <Gift className={`w-6 h-6 ${iconColor}`} />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping"></div>
          <div className="absolute inset-0 bg-yellow-300 rounded-xl opacity-20 animate-pulse"></div>
        </div>
      );
    default:
      return <ClipboardList className={`w-6 h-6 ${iconColor}`} />;
  }
};








// Data fetching function
const fetchOrdersByTab = async (tab: TabType): Promise<Order[]> => {
  try {
    const response = await fetch(`/api/orders?tab=${tab}`);
    const data = await response.json();
    return data.success ? data.data : [];
  } catch (error) {
    console.error('Error fetching orders:', error);
    return [];
  }
};

// Function to update order status
const updateOrderStatus = async (
  orderId: string,
  newStatus: OrderStatus,
  actionLog?: ActionLog,
  paymentInfo?: Partial<PaymentInfo>,
  scheduledDate?: string,
  deliveryAgent?: string,
  cancellationReason?: string
): Promise<Order | null> => {
  try {
    const response = await fetch('/api/orders', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: orderId,
        status: newStatus,
        actionLog,
        paymentInfo,
        scheduledDate,
        deliveryAgent,
        cancellationReason
      }),
    });

    const data = await response.json();
    return data.success ? data.data : null;
  } catch (error) {
    console.error('Error updating order status:', error);
    return null;
  }
};

// Function to get current location
const getCurrentLocation = (): Promise<{ lat: number; lng: number; address: string }> => {
  return new Promise((resolve) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            address: 'Current Location' // In real app, reverse geocode this
          });
        },
        () => {
          // Fallback to a default Cairo location if GPS is unavailable
          resolve({
            lat: 30.0444,
            lng: 31.2357,
            address: 'Downtown Cairo (Default Location)'
          });
        }
      );
    } else {
      // Fallback to a default Cairo location if geolocation is not supported
      resolve({
        lat: 30.0444,
        lng: 31.2357,
        address: 'Downtown Cairo (Default Location)'
      });
    }
  });
};

// Supplier Payment Modal Component
const SupplierPaymentModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (amount: string) => void;
}> = ({ isOpen, onClose, onConfirm }) => {
  const t = useOrdersTranslations();
  const [amount, setAmount] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const handleConfirm = () => {
    if (!amount || parseFloat(amount) <= 0) {
      setError(t('pleaseEnterValidAmount'));
      return;
    }
    onConfirm(amount);
    setAmount('');
    setError(null);
  };

  if (!isOpen) return null;

  return (
    <CompactModal 
      isOpen={isOpen} 
      onClose={onClose} 
      title={t('confirmSupplierPayment')}
    >
      <form onSubmit={(e) => { e.preventDefault(); handleConfirm(); }}>
        <TwoColumnForm>
          <FullWidthField>
            <FormField label={`${t('paymentAmount')} (EGP) *`}>
              <IconInput
                icon={<DollarSign className="w-4 h-4" />}
                type="number"
                min="0"
                step="0.01"
                value={amount}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  setAmount(e.target.value);
                  setError(null);
                }}
                placeholder="0.00"
                className={error ? 'border-red-500' : ''}
              />
              {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
            </FormField>
          </FullWidthField>
        </TwoColumnForm>
        
        <ModalActions
          onCancel={onClose}
          cancelText={t('cancel')}
          submitText={t('confirm')}
          icon={<CheckCircle className="w-4 h-4" />}
          cancelIcon={<X className="w-4 h-4" />}
        />
      </form>
    </CompactModal>
  );
};

// Customer Payment Modal Component
const CustomerPaymentModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (paymentData: { amount: string; method: string; deliveryStatus: string }) => void;
}> = ({ isOpen, onClose, onConfirm }) => {
  const t = useOrdersTranslations();
  const [paymentData, setPaymentData] = useState({
    amount: '',
    method: 'Cash',
    deliveryStatus: 'Full'
  });
  const [errors, setErrors] = useState<{amount?: string}>({});

  const handleConfirm = () => {
    const newErrors: {amount?: string} = {};
    
    if (!paymentData.amount || parseFloat(paymentData.amount) <= 0) {
      newErrors.amount = t('pleaseEnterValidAmount');
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    onConfirm(paymentData);
    setPaymentData({
      amount: '',
      method: 'Cash',
      deliveryStatus: 'Full'
    });
    setErrors({});
  };

  if (!isOpen) return null;

  return (
    <CompactModal 
      isOpen={isOpen} 
      onClose={onClose} 
      title={t('confirmCustomerPayment')}
    >
      <form onSubmit={(e) => { e.preventDefault(); handleConfirm(); }}>
        <TwoColumnForm>
          <FormField label={`${t('paymentAmount')} (EGP) *`}>
            <IconInput
              icon={<DollarSign className="w-4 h-4" />}
              type="number"
              min="0"
              step="0.01"
              value={paymentData.amount}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                setPaymentData({...paymentData, amount: e.target.value});
                setErrors({...errors, amount: undefined});
              }}
              placeholder="0.00"
              className={errors.amount ? 'border-red-500' : ''}
            />
            {errors.amount && <p className="text-red-500 text-xs mt-1">{errors.amount}</p>}
          </FormField>
          
          <FormField label={t('paymentMethod')}>
            <IconSelect
              icon={<DollarSign className="w-4 h-4" />}
              value={paymentData.method}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setPaymentData({...paymentData, method: e.target.value})}
            >
              <option value="Cash">{t('paymentMethods.cash')}</option>
              <option value="Card">{t('paymentMethods.card')}</option>
              <option value="Digital">{t('paymentMethods.digital')}</option>
              <option value="Other">{t('paymentMethods.other')}</option>
            </IconSelect>
          </FormField>
          
          <FullWidthField>
            <FormField label={t('deliveryStatus')}>
              <div className="flex flex-wrap gap-3 pt-1">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="deliveryStatus"
                    value="Full"
                    checked={paymentData.deliveryStatus === 'Full'}
                    onChange={() => setPaymentData({...paymentData, deliveryStatus: 'Full'})}
                    className="text-indigo-600 focus:ring-indigo-500"
                  />
                  <span className="text-sm text-gray-700">{t('deliveryStatuses.full')}</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="deliveryStatus"
                    value="Partial"
                    checked={paymentData.deliveryStatus === 'Partial'}
                    onChange={() => setPaymentData({...paymentData, deliveryStatus: 'Partial'})}
                    className="text-indigo-600 focus:ring-indigo-500"
                  />
                  <span className="text-sm text-gray-700">{t('deliveryStatuses.partial')}</span>
                </label>
              </div>
            </FormField>
          </FullWidthField>
        </TwoColumnForm>
        
        <ModalActions
          onCancel={onClose}
          cancelText={t('cancel')}
          submitText={t('confirm')}
          icon={<CheckCircle className="w-4 h-4" />}
          cancelIcon={<X className="w-4 h-4" />}
        />
      </form>
    </CompactModal>
  );
};

// Reschedule Modal Component
const RescheduleModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (newDate: string) => void;
}> = ({ isOpen, onClose, onConfirm }) => {
  const t = useOrdersTranslations();
  const [newDate, setNewDate] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const handleConfirm = () => {
    if (!newDate) {
      setError(t('pleaseSelectDate'));
      return;
    }
    
    const selectedDate = new Date(newDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate < today) {
      setError(t('dateCannotBeInPast'));
      return;
    }
    
    onConfirm(newDate);
    setNewDate('');
    setError(null);
  };

  if (!isOpen) return null;

  return (
    <CompactModal 
      isOpen={isOpen} 
      onClose={onClose} 
      title={t('rescheduleDelivery')}
    >
      <form onSubmit={(e) => { e.preventDefault(); handleConfirm(); }}>
        <TwoColumnForm>
          <FullWidthField>
            <FormField label={`${t('newDeliveryDate')} *`}>
              <IconInput
                icon={<Calendar className="w-4 h-4" />}
                type="date"
                value={newDate}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  setNewDate(e.target.value);
                  setError(null);
                }}
                min={new Date().toISOString().split('T')[0]}
                className={error ? 'border-red-500' : ''}
              />
              {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
            </FormField>
          </FullWidthField>
        </TwoColumnForm>
        
        <ModalActions
          onCancel={onClose}
          cancelText={t('cancel')}
          submitText={t('reschedule')}
          icon={<Calendar className="w-4 h-4" />}
          cancelIcon={<X className="w-4 h-4" />}
        />
      </form>
    </CompactModal>
  );
};

// Cancellation Modal Component
const CancellationModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string, notes?: string) => void;
}> = ({ isOpen, onClose, onConfirm }) => {
  const t = useOrdersTranslations();
  const [reason, setReason] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const handleConfirm = () => {
    if (!reason) {
      setError(t('pleaseSelectReason'));
      return;
    }
    
    onConfirm(reason, notes);
    setReason('');
    setNotes('');
    setError(null);
  };

  if (!isOpen) return null;

  return (
    <CompactModal 
      isOpen={isOpen} 
      onClose={onClose} 
      title={t('cancelOrder')}
    >
      <form onSubmit={(e) => { e.preventDefault(); handleConfirm(); }}>
        <TwoColumnForm>
          <FullWidthField>
            <FormField label={`${t('cancellationReason')} *`}>
              <IconSelect
                icon={<XCircle className="w-4 h-4" />}
                value={reason}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                  setReason(e.target.value);
                  setError(null);
                }}
                className={error ? 'border-red-500' : ''}
              >
                <option value="">{t('selectReason')}...</option>
                <option value="Customer Request">{t('cancellationReasons.customerRequest')}</option>
                <option value="Out of Stock">{t('cancellationReasons.outOfStock')}</option>
                <option value="Delivery Issues">{t('cancellationReasons.deliveryIssues')}</option>
                <option value="Payment Issues">{t('cancellationReasons.paymentIssues')}</option>
                <option value="Changed Mind">{t('cancellationReasons.changedMind')}</option>
                <option value="Other">{t('cancellationReasons.other')}</option>
              </IconSelect>
              {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
            </FormField>
          </FullWidthField>
          
          <FullWidthField>
            <FormField label={t('additionalNotes')}>
              <div className="relative">
                <textarea
                  value={notes}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setNotes(e.target.value)}
                  rows={2}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pl-9 text-sm resize-none"
                  placeholder={t('enterAdditionalNotes')}
                />
                <span className="absolute left-3 top-3 text-gray-400">
                  <ClipboardList className="w-4 h-4" />
                </span>
              </div>
            </FormField>
          </FullWidthField>
        </TwoColumnForm>
        
        <ModalActions
          onCancel={onClose}
          cancelText={t('goBack')}
          submitText={t('confirmCancellation')}
          icon={<XCircle className="w-4 h-4" />}
          cancelIcon={<X className="w-4 h-4" />}
        />
      </form>
    </CompactModal>
  );
};

// Re-assign Delivery Agent Modal Component
const ReassignModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (deliveryAgent: string) => void;
  currentAgent?: string;
}> = ({ isOpen, onClose, onConfirm, currentAgent }) => {
  const t = useOrdersTranslations();
  const [deliveryAgent, setDeliveryAgent] = useState<string>('');
  const [deliveryAgents, setDeliveryAgents] = useState<Array<{ id: string; name: string }>>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Load delivery agents
  useEffect(() => {
    if (isOpen) {
      loadDeliveryAgents();
      // Reset selection when modal opens
      setDeliveryAgent('');
      setError(null);
    }
  }, [isOpen]);

  const loadDeliveryAgents = async () => {
    try {
      setLoading(true);
      // Use Firebase service to get delivery team members
      const { TeamService } = await import('@/services/firebase/team');
      const firebaseTeamMembers = await TeamService.getAllTeamMembers();

      // Filter and format delivery agents
      const agents = firebaseTeamMembers
        .filter(member => member.role === 'Delivery' && member.status === 'Active')
        .map(member => ({
          id: member.id,
          name: member.name
        }));

      setDeliveryAgents(agents);
    } catch (error) {
      console.error('Error loading delivery agents:', error);
      setDeliveryAgents([]);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = () => {
    if (!deliveryAgent) {
      setError(t('pleaseSelectDeliveryAgent'));
      return;
    }
    
    onConfirm(deliveryAgent);
    setDeliveryAgent('');
    setError(null);
  };

  if (!isOpen) return null;

  return (
    <CompactModal 
      isOpen={isOpen} 
      onClose={onClose} 
      title={t('reassignDelivery')}
    >
      <form onSubmit={(e) => { e.preventDefault(); handleConfirm(); }}>
        <TwoColumnForm>
          {currentAgent && (
            <FullWidthField>
              <FormField label={t('currentAgent')}>
                <IconInput
                  icon={<Users className="w-4 h-4" />}
                  value={currentAgent}
                  readOnly
                  className="bg-gray-50 text-gray-600 cursor-not-allowed"
                />
              </FormField>
            </FullWidthField>
          )}
          
          <FullWidthField>
            <FormField label={`${t('newDeliveryAgent')} *`}>
              {loading ? (
                <div className="flex items-center p-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600 mr-2"></div>
                  <span className="text-sm text-gray-600">{t('loadingAgents')}...</span>
                </div>
              ) : (
                <>
                  <IconSelect
                    icon={<Truck className="w-4 h-4" />}
                    value={deliveryAgent}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                      setDeliveryAgent(e.target.value);
                      setError(null);
                    }}
                    className={error ? 'border-red-500' : ''}
                  >
                    <option value="">{t('selectAgent')}...</option>
                    {deliveryAgents.map((agent) => (
                      <option key={agent.id} value={agent.name}>
                        {agent.name}
                      </option>
                    ))}
                  </IconSelect>
                  {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
                </>
              )}
            </FormField>
          </FullWidthField>
        </TwoColumnForm>
        
        <ModalActions
          onCancel={onClose}
          cancelText={t('cancel')}
          submitText={t('reassign')}
          icon={<Users className="w-4 h-4" />}
          cancelIcon={<X className="w-4 h-4" />}
        />
      </form>
    </CompactModal>
  );
};

// Order Details Modal Component
const OrderDetailsModal: React.FC<{
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onStatusUpdate: (orderId: string, newStatus: OrderStatus, actionLog?: ActionLog, paymentInfo?: Partial<PaymentInfo>) => void;
  onReschedule?: (orderId: string, newDate: string) => void;

  onEdit: (order: Order) => void;
  setShowRescheduleModal: (show: boolean) => void;
  setShowReassignModal: (show: boolean) => void;
  setShowCancellationModal: (show: boolean) => void;
  expandedStages: {[key: string]: boolean};
  toggleStageExpansion: (orderId: string, status: OrderStatus) => void;
  getStatusTranslation: (status: OrderStatus) => string;
}> = ({ order, isOpen, onClose, onStatusUpdate, onEdit, setShowRescheduleModal, setShowReassignModal, setShowCancellationModal, expandedStages, toggleStageExpansion, getStatusTranslation }) => {

  const ordersT = useOrdersTranslations();
  if (!isOpen || !order) return null;

  // Handle click outside to close
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 animate-fadeIn"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-3xl p-8 max-w-2xl w-full mx-4 shadow-2xl animate-scaleIn max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{ordersT('orderDetails.title')}</h2>
            <p className="text-indigo-600 font-semibold">{order.id}</p>
          </div>
          {order.status !== 'Delivered' && order.status !== 'Cancelled' && (
            <button
              onClick={() => onEdit(order)}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-xl font-semibold text-sm hover:from-blue-700 hover:to-indigo-700 transition duration-200 shadow-lg hover:shadow-xl flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              {ordersT('orderDetails.edit')}
            </button>
          )}
        </div>

        {/* Order Status */}
        <div className="mb-6">
          <span className={`inline-flex items-center px-4 py-2 text-sm font-bold rounded-full ${getStatusClass(order.status)}`}>
            <div className="w-2 h-2 rounded-full bg-current mr-2"></div>
            {getStatusTranslation(order.status)}
          </span>
        </div>

        {/* Customer Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-gray-50 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Users className="w-5 h-5 text-indigo-600" />
              Customer Information
            </h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold">
                  {order.avatar ? (
                    <Image src={order.avatar} alt={order.customer} width={40} height={40} className="w-full h-full rounded-full object-cover" />
                  ) : (
                    order.customer.charAt(0)
                  )}
                </div>
                <div>
                  <p className="font-semibold text-gray-900">{order.customer}</p>
                  <p className="text-sm text-gray-500">Customer</p>
                </div>
              </div>
              {order.phone && (
                <div className="flex items-center gap-2 text-sm">
                  <span className="font-medium text-gray-700">Phone:</span>
                  <span className="text-gray-600">{order.phone}</span>
                </div>
              )}
            </div>
          </div>

          <div className="bg-gray-50 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-indigo-600" />
              Delivery Information
            </h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-700 mb-1">Address:</p>
                <p className="text-gray-600">{order.address || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 mb-1">Order Date:</p>
                <p className="text-gray-600">{order.date}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 mb-1">Total Amount:</p>
                <p className="text-xl font-bold text-indigo-600">{order.amount}</p>
              </div>
              {order.estimatedDeliveryTime && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Estimated Delivery:</p>
                  <p className="text-gray-600">{order.estimatedDeliveryTime}</p>
                </div>
              )}
              {order.depositAmount && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Deposit Amount:</p>
                  <p className="text-gray-600">{order.depositAmount}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Team Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-blue-50 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              Sales Team
            </h3>
            <div className="space-y-3">
              {order.salesRep && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Sales Representative:</p>
                  <p className="text-gray-900 font-semibold">{order.salesRep}</p>
                </div>
              )}
              {order.purchasingRep && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Purchasing Representative:</p>
                  <p className="text-gray-900 font-semibold">{order.purchasingRep}</p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-green-50 rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Truck className="w-5 h-5 text-green-600" />
              Delivery Team
            </h3>
            <div className="space-y-3">
              {order.deliveryAgent && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Delivery Agent:</p>
                  <p className="text-gray-900 font-semibold">{order.deliveryAgent}</p>
                </div>
              )}
              {order.cancellationReason && (
                <div>
                  <p className="text-sm font-medium text-red-700 mb-1">Cancellation Reason:</p>
                  <p className="text-red-600 text-sm italic">{order.cancellationReason}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Order Items */}
        {order.items && order.items.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Package className="w-5 h-5 text-indigo-600" />
              Order Items
            </h3>
            <div className="bg-gray-50 rounded-2xl p-6">
              <div className="space-y-3">
                {order.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                    <div>
                      <p className="font-medium text-gray-900">{item.name}</p>
                      <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
                    </div>
                    <p className="font-semibold text-gray-900">{item.price}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Unified Interactive Timeline */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
            <Clock className="w-5 h-5 text-slate-600" />
            Interactive Order Timeline
          </h3>

          {/* Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-6 top-0 bottom-0 w-1 bg-gradient-to-b from-slate-200 via-slate-300 to-slate-200 rounded-full"></div>

            {/* Timeline Steps */}
            <div className="space-y-6">
              {(['Pending', 'Started', 'Moved to Supplier', 'Arrived at Supplier', 'Moving to Customer', 'Arrived at Customer', 'Delivered'] as OrderStatus[]).map((status) => {
                const currentStatusIndex = getStatusIndex(order.status);
                const stepStatusIndex = getStatusIndex(status);
                const isCompleted = currentStatusIndex >= stepStatusIndex;
                const isCurrent = order.status === status;
                const isNext = statusFlow[order.status] === status;
                const isClickable = isNext && order.status !== 'Delivered' && order.status !== 'Cancelled';

                // Find the action log entry for this status
                const actionForStatus = order.actionLog?.find(action => action.status === status);

                // Check if this stage is expanded
                const isExpanded = expandedStages[`${order.id}-${status}`] ?? true; // Default to expanded

                return (
                  <div
                    key={status}
                    className="relative"
                  >
                    {/* Foldable Stage Header */}
                    <div className="flex items-start gap-4 w-full">
                      {/* Timeline Dot */}
                      <div className={`relative z-10 w-12 h-12 rounded-full flex items-center justify-center border-3 transition-all duration-300 shadow-lg ${
                        isCompleted
                          ? 'bg-white border-slate-300 shadow-slate-200/50'
                          : isCurrent
                            ? 'bg-white border-amber-300 shadow-amber-200/50 animate-pulse'
                            : isClickable
                              ? 'bg-white border-blue-300 shadow-blue-200/50 hover:shadow-blue-300/70'
                              : 'bg-slate-100 border-slate-300 shadow-slate-200/30'
                      }`}>
                        {isCompleted || isCurrent || isClickable ? (
                          getStatusIcon(status)
                        ) : (
                          <div className="w-4 h-4 rounded-full bg-slate-400"></div>
                        )}
                      </div>

                      {/* Stage Header - Always Visible */}
                      <div className="flex-1 min-w-0">
                        <div
                          className="flex items-center justify-between p-4 rounded-2xl border-2 cursor-pointer transition-all duration-300 hover:shadow-md bg-white border-slate-200"
                          onClick={() => toggleStageExpansion(order.id, status)}
                        >
                          <div className="flex items-center gap-3">
                            <h4 className="font-bold text-lg text-black">{getStatusTranslation(status)}</h4>
                            <div className={`px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wide ${
                              isCompleted
                                ? 'bg-slate-100 text-slate-700 border border-slate-200'
                                : isCurrent
                                  ? 'bg-amber-100 text-amber-700 border border-amber-200'
                                  : isClickable
                                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                                    : 'bg-slate-100 text-slate-600 border border-slate-200'
                            }`}>
                              {isCompleted ? 'Completed' : isCurrent ? 'In Progress' : isClickable ? 'Ready' : 'Pending'}
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            {/* Click to advance button for clickable stages */}
                            {isClickable && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // Prevent triggering fold/unfold
                                  onStatusUpdate(order.id, status);
                                }}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200 flex items-center gap-1"
                              >
                                <ArrowRight className="w-3 h-3" />
                                Advance
                              </button>
                            )}

                            {/* Fold/Unfold Toggle */}
                            <button className="p-1 rounded-full hover:bg-slate-100 transition-colors duration-200">
                              {isExpanded ? (
                                <ChevronUp className="w-5 h-5 text-slate-600" />
                              ) : (
                                <ChevronDown className="w-5 h-5 text-slate-600" />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Expandable Content */}
                    {isExpanded && (
                      <div className="ml-16 mt-4">
                        <div className={`p-5 rounded-2xl border-2 transition-all duration-300 ${
                          isCompleted
                            ? 'bg-white border-slate-300 shadow-lg shadow-slate-200/50'
                            : isCurrent
                              ? 'bg-white border-amber-200 shadow-lg shadow-amber-100/50'
                              : isClickable
                                ? 'bg-white border-blue-200 shadow-md'
                                : 'bg-slate-50 border-slate-200 shadow-sm'
                        }`}>
                          {/* Status Description */}
                          <p className="text-sm mb-4 leading-relaxed text-black">
                            {statusDescriptions[status]}
                          </p>

                        {/* Action Details (if completed) */}
                        {actionForStatus && (
                          <div className="space-y-3 mt-4 pt-4 border-t border-slate-200">
                            {/* Timestamp */}
                            <div className="flex items-center gap-3">
                              <div className={`p-1.5 rounded-lg ${
                                isCompleted ? 'bg-indigo-100' : 'bg-amber-100'
                              }`}>
                                <Clock className={`w-4 h-4 ${
                                  isCompleted ? 'text-indigo-600' : 'text-amber-600'
                                }`} />
                              </div>
                              <span className="text-sm font-medium text-black">
                                {actionForStatus.timestamp}
                              </span>
                            </div>

                            {/* Location */}
                            <div className="flex items-center gap-3">
                              <div className={`p-1.5 rounded-lg ${
                                isCompleted ? 'bg-indigo-100' : 'bg-amber-100'
                              }`}>
                                <MapPin className={`w-4 h-4 ${
                                  isCompleted ? 'text-indigo-600' : 'text-amber-600'
                                }`} />
                              </div>
                              <span className="text-sm font-medium text-black">
                                {actionForStatus.location}
                              </span>
                              {actionForStatus.gpsCoordinates && actionForStatus.gpsCoordinates.lat !== 0 && actionForStatus.gpsCoordinates.lng !== 0 && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation(); // Prevent triggering status update
                                    const { lat, lng } = actionForStatus.gpsCoordinates!;
                                    const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}&z=15`;
                                    window.open(googleMapsUrl, '_blank');
                                  }}
                                  className="flex items-center justify-center w-7 h-7 bg-slate-100 hover:bg-slate-200 rounded-lg transition-colors duration-200 group shadow-sm border border-slate-200"
                                  title="View location on Google Maps"
                                >
                                  <MapPin className="w-3.5 h-3.5 text-slate-600 group-hover:text-slate-800" />
                                </button>
                              )}
                            </div>

                            {/* User */}
                            <div className="flex items-center gap-3">
                              <div className={`p-1.5 rounded-lg ${
                                isCompleted ? 'bg-indigo-100' : 'bg-amber-100'
                              }`}>
                                <Users className={`w-4 h-4 ${
                                  isCompleted ? 'text-indigo-600' : 'text-amber-600'
                                }`} />
                              </div>
                              <span className="text-sm text-black">
                                Action by <span className="font-semibold">{actionForStatus.userName}</span>
                              </span>
                            </div>

                            {/* Payment Information for this stage */}
                            {status === 'Moving to Customer' && order.paymentInfo?.supplierPayment && (
                              <div className="mt-3 p-4 rounded-xl border-l-4 bg-blue-50 border-blue-400">
                                <div className="flex items-center gap-2 mb-2">
                                  <Package className="w-4 h-4 text-blue-600" />
                                  <span className="text-sm font-semibold text-blue-900">Supplier Payment Confirmed</span>
                                </div>
                                <div className="space-y-1 text-sm text-blue-800">
                                  <p><span className="font-medium">Amount:</span> {order.paymentInfo.supplierPayment.amount}</p>
                                  <p><span className="font-medium">Confirmed by:</span> {order.paymentInfo.supplierPayment.confirmedBy}</p>
                                  <p><span className="font-medium">Time:</span> {order.paymentInfo.supplierPayment.timestamp}</p>
                                </div>
                              </div>
                            )}

                            {status === 'Delivered' && order.paymentInfo?.customerPayment && (
                              <div className="mt-3 p-4 rounded-xl border-l-4 bg-green-50 border-green-400">
                                <div className="flex items-center gap-2 mb-2">
                                  <CheckCircle className="w-4 h-4 text-green-600" />
                                  <span className="text-sm font-semibold text-green-900">Customer Payment Received</span>
                                </div>
                                <div className="space-y-1 text-sm text-green-800">
                                  <p><span className="font-medium">Amount:</span> {order.paymentInfo.customerPayment.amount}</p>
                                  <p><span className="font-medium">Method:</span> {order.paymentInfo.customerPayment.method}</p>
                                  <p><span className="font-medium">Delivery Status:</span> {order.paymentInfo.customerPayment.deliveryStatus}</p>
                                  <p><span className="font-medium">Received by:</span> {order.paymentInfo.customerPayment.receivedBy}</p>
                                </div>
                              </div>
                            )}

                            {/* Notes */}
                            {actionForStatus.notes && (
                              <div className={`mt-3 p-4 rounded-xl border-l-4 ${
                                isCompleted
                                  ? 'bg-indigo-50 border-indigo-400'
                                  : 'bg-amber-50 border-amber-400'
                              }`}>
                                <p className="text-sm italic leading-relaxed text-black">
                                  &quot;{actionForStatus.notes}&quot;
                                </p>
                              </div>
                            )}
                          </div>
                        )}

                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Action Buttons */}
          {order.status !== 'Delivered' && order.status !== 'Cancelled' && (
            <div className="flex flex-wrap gap-2 mt-6 pt-4 border-t border-gray-200">
              <button
                onClick={() => setShowRescheduleModal(true)}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-xl font-semibold text-sm hover:from-blue-700 hover:to-indigo-700 transition duration-200 shadow-lg hover:shadow-xl flex items-center gap-2"
              >
                <Calendar className="w-4 h-4" />
                Reschedule
              </button>
              <button
                onClick={() => setShowReassignModal(true)}
                className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-xl font-semibold text-sm hover:from-purple-700 hover:to-pink-700 transition duration-200 shadow-lg hover:shadow-xl flex items-center gap-2"
              >
                <Users className="w-4 h-4" />
                Re-assign
              </button>
              <button
                onClick={() => setShowCancellationModal(true)}
                className="bg-gradient-to-r from-red-600 to-red-700 text-white px-4 py-2 rounded-xl font-semibold text-sm hover:from-red-700 hover:to-red-800 transition duration-200 shadow-lg hover:shadow-xl flex items-center gap-2"
              >
                <X className="w-4 h-4" />
                Cancel Order
              </button>
            </div>
          )}
        </div>

        {/* Payment Information */}
        {(order.paymentInfo?.supplierPayment || order.paymentInfo?.customerPayment) && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Package className="w-5 h-5 text-indigo-600" />
              Payment Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {order.paymentInfo.supplierPayment && (
                <div className="bg-blue-50 rounded-2xl p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">Supplier Payment</h4>
                  <p className="text-sm text-blue-700">Amount: {order.paymentInfo.supplierPayment.amount}</p>
                  <p className="text-sm text-blue-700">Confirmed by: {order.paymentInfo.supplierPayment.confirmedBy}</p>
                  <p className="text-sm text-blue-700">Time: {order.paymentInfo.supplierPayment.timestamp}</p>
                </div>
              )}
              {order.paymentInfo.customerPayment && (
                <div className="bg-green-50 rounded-2xl p-4">
                  <h4 className="font-semibold text-green-900 mb-2">Customer Payment</h4>
                  <p className="text-sm text-green-700">Amount: {order.paymentInfo.customerPayment.amount}</p>
                  <p className="text-sm text-green-700">Method: {order.paymentInfo.customerPayment.method}</p>
                  <p className="text-sm text-green-700">Status: {order.paymentInfo.customerPayment.deliveryStatus}</p>
                  <p className="text-sm text-green-700">Received by: {order.paymentInfo.customerPayment.receivedBy}</p>
                </div>
              )}
            </div>
          </div>
        )}




      </div>
    </div>
  );
};

// Quick Order Form Data Types
type QuickOrderFormData = {
  orderType: string;
  customerName: string;
  customerAddress: string;
  customerPhone: string;
  salesRep: string;
  orderDate: string;
  estimatedDeliveryTime: string;
  depositAmount: string;
  totalOrderAmount: string;
  deliveryAgent: string;
  driverId: string;
  truckId: string;
  driverName?: string;
  truckLicensePlate?: string;
};

// Quick Order Modal Component
const QuickOrderModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (orderData: QuickOrderFormData) => Promise<void>;
  editOrder?: Order | null;
}> = ({ isOpen, onClose, onSave, editOrder }) => {
  const t = useCommonTranslations();
  const ordersT = useOrdersTranslations();
  const [formData, setFormData] = useState<QuickOrderFormData>({
    orderType: '',
    customerName: '',
    customerAddress: '',
    customerPhone: '',
    salesRep: '',
    orderDate: new Date().toISOString().split('T')[0],
    estimatedDeliveryTime: '',
    depositAmount: '',
    totalOrderAmount: '',
    deliveryAgent: '',
    driverId: '',
    truckId: '',
  });

  const [errors, setErrors] = useState<Partial<QuickOrderFormData>>({});
  const [orderId, setOrderId] = useState('');
  const [teamMembers, setTeamMembers] = useState<Array<{ id: string; name: string; role: string }>>([]);
  const [drivers, setDrivers] = useState<Array<{ id: string; name: string; status: string; truckId?: string; truckLicensePlate?: string }>>([]);
  const [trucks, setTrucks] = useState<Array<{ id: string; licensePlate: string; model: string; status: string; driverId?: string; driverName?: string }>>([]);

  // Order types mapping
  const orderTypes = [
    { key: 'paints', display: 'الدهانات (Paints)' },
    { key: 'electricalSupplies', display: 'اللوازم الكهربائية (Electrical Supplies)' },
    { key: 'bathroomFixtures', display: 'أدوات الحمام وترتيب المرحاض (Bathroom Fixtures & Toilet Accessories)' },
    { key: 'plumbingSupplies', display: 'لوازم السباكة (Plumbing Supplies)' },
    { key: 'gypsumBoards', display: 'ألواح الجبس (Gypsum Boards)' },
    { key: 'ceramicTiles', display: 'بلاط السيراميك والبورسلين (Ceramic & Porcelain Tiles)' },
    { key: 'airConditioning', display: 'تكييفات (Air Conditioning)' },
    { key: 'hdfFlooring', display: 'بلاط الأرضيات HDF (HDF Flooring)' },
    { key: 'tvs', display: 'التلفاز والشاشات (TVs & Screens)' },
    { key: 'homeAppliances', display: 'أجهزة منزلية (Home Appliances)' },
  ];



  // Load team members
  const loadTeamMembers = async () => {
    try {
      // Use Firebase service instead of local API
      const { TeamService } = await import('@/services/firebase/team');
      const firebaseTeamMembers = await TeamService.getAllTeamMembers();

      // Convert Firebase team members to the format expected by the UI
      const mapFirebaseRoleToUI = (firebaseRole: string) => {
        const roleMap: Record<string, string> = {
          'Sales': 'Sales',
          'Purchasing': 'Purchasing',
          'Delivery': 'Delivery',
          'Administration': 'Management',
          'Customer Service': 'Support'
        };
        return roleMap[firebaseRole] || 'Support';
      };

      const formattedMembers = firebaseTeamMembers.map(member => ({
        id: member.id,
        name: member.name,
        role: mapFirebaseRoleToUI(member.role),
        email: member.email,
        phone: member.phone,
        location: member.department, // Use department as location
        status: member.status,
        joinDate: member.joinDate.toDate().toISOString().split('T')[0],
        avatar: member.avatar
      }));

      setTeamMembers(formattedMembers);
    } catch (error) {
      console.error('Error loading team members:', error);
      setTeamMembers([]); // Ensure it's always an array
    }
  };

  // Load drivers
  const loadDrivers = async () => {
    try {
      const { FleetService } = await import('@/services/firebase/fleet');
      const firebaseDrivers = await FleetService.getAllDrivers();

      const formattedDrivers = firebaseDrivers.map(driver => ({
        id: driver.id,
        name: driver.name,
        status: driver.status,
        truckId: driver.truckId,
        truckLicensePlate: driver.truckLicensePlate
      }));

      setDrivers(formattedDrivers);
    } catch (error) {
      console.error('Error loading drivers:', error);
      setDrivers([]);
    }
  };

  // Load trucks
  const loadTrucks = async () => {
    try {
      const { FleetService } = await import('@/services/firebase/fleet');
      const firebaseTrucks = await FleetService.getAllTrucks();

      const formattedTrucks = firebaseTrucks.map(truck => ({
        id: truck.id,
        licensePlate: truck.licensePlate,
        model: truck.model,
        status: truck.status,
        driverId: truck.driverId,
        driverName: truck.driverName
      }));

      setTrucks(formattedTrucks);
    } catch (error) {
      console.error('Error loading trucks:', error);
      setTrucks([]);
    }
  };

  // Generate order ID when modal opens
  useEffect(() => {
    if (isOpen) {
      const generateOrderId = () => {
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `ORD-${randomNum}`;
      };

      // Load team members, drivers, and trucks
      loadTeamMembers();
      loadDrivers();
      loadTrucks();

      if (editOrder) {
        // Populate form with existing order data for editing
        setOrderId(editOrder.id);
        setFormData({
          orderType: editOrder.items?.[0]?.name || '', // Assuming order type is stored in first item
          customerName: editOrder.customer,
          customerAddress: editOrder.address || '',
          customerPhone: editOrder.phone || '',
          salesRep: editOrder.salesRep || '',
          orderDate: editOrder.date || new Date().toISOString().split('T')[0],
          estimatedDeliveryTime: editOrder.estimatedDeliveryTime || '',
          depositAmount: editOrder.depositAmount || '',
          totalOrderAmount: editOrder.amount?.replace(/[^\d.]/g, '') || '', // Remove currency symbols
          deliveryAgent: editOrder.deliveryAgent || '',
          driverId: editOrder.driverId || '',
          truckId: editOrder.truckId || '',
          driverName: editOrder.driverName || '',
          truckLicensePlate: editOrder.truckLicensePlate || ''
        });
      } else {
        // Reset form for new order
        setOrderId(generateOrderId());
        setFormData({
          orderType: '',
          customerName: '',
          customerAddress: '',
          customerPhone: '',
          salesRep: '',
          orderDate: new Date().toISOString().split('T')[0],
          estimatedDeliveryTime: '',
          depositAmount: '',
          totalOrderAmount: '',
          deliveryAgent: '',
          driverId: '',
          truckId: '',
        });
      }
      setErrors({});
    }
  }, [isOpen, editOrder]);

  const handleInputChange = (field: keyof QuickOrderFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Handle driver selection and auto-filter trucks
  const handleDriverChange = (driverId: string) => {
    setFormData(prev => ({ ...prev, driverId, truckId: '' })); // Clear truck selection when driver changes
    // Clear errors
    if (errors.driverId) {
      setErrors(prev => ({ ...prev, driverId: undefined, truckId: undefined }));
    }
  };

  // Get available trucks based on selected driver
  const getAvailableTrucks = () => {
    if (!formData.driverId) {
      // If no driver selected, show all active trucks
      return trucks.filter(truck => truck.status === 'Active');
    }

    const selectedDriver = drivers.find(d => d.id === formData.driverId);
    if (selectedDriver?.truckId) {
      // If driver has an assigned truck, show only that truck
      return trucks.filter(truck => truck.id === selectedDriver.truckId);
    } else {
      // If driver has no assigned truck, show unassigned trucks
      return trucks.filter(truck => truck.status === 'Active' && !truck.driverId);
    }
  };

  // Get driver assignment info for display
  const getDriverAssignmentInfo = (driverId: string) => {
    const driver = drivers.find(d => d.id === driverId);
    if (driver?.truckId && driver?.truckLicensePlate) {
      return `${t('driverAssignment.currentlyAssignedTo')} ${driver.truckLicensePlate}`;
    }
    return t('driverAssignment.noTruckAssigned');
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<QuickOrderFormData> = {};

    if (!formData.orderType) newErrors.orderType = t('validation.orderTypeRequired');
    if (!formData.customerName) newErrors.customerName = t('validation.customerNameRequired');
    if (!formData.customerAddress) newErrors.customerAddress = t('validation.customerAddressRequired');
    if (!formData.customerPhone) newErrors.customerPhone = t('validation.customerPhoneRequired');
    if (!formData.salesRep) newErrors.salesRep = t('validation.salesRepRequired');
    if (!formData.orderDate) newErrors.orderDate = t('validation.orderDateRequired');
    if (!formData.estimatedDeliveryTime) newErrors.estimatedDeliveryTime = t('validation.estimatedDeliveryTimeRequired');
    if (!formData.depositAmount) newErrors.depositAmount = t('validation.depositAmountRequired');
    if (!formData.totalOrderAmount) newErrors.totalOrderAmount = t('validation.totalOrderAmountRequired');
    if (!formData.deliveryAgent) newErrors.deliveryAgent = t('validation.deliveryAgentRequired');
    if (!formData.driverId) newErrors.driverId = t('validation.driverRequired');
    if (!formData.truckId) newErrors.truckId = t('validation.truckRequired');

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      // Get driver and truck names for the order data
      const selectedDriver = drivers.find(d => d.id === formData.driverId);
      const selectedTruck = trucks.find(t => t.id === formData.truckId);

      const orderDataWithNames = {
        ...formData,
        driverName: selectedDriver?.name || '',
        truckLicensePlate: selectedTruck?.licensePlate || ''
      };

      await onSave(orderDataWithNames);
      onClose();
    } catch (error) {
      console.error('Error saving order:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <CompactModal 
      isOpen={isOpen} 
      onClose={onClose} 
      title={editOrder ? t('editOrder') : t('quickOrder')}
      maxWidth="max-w-3xl"
    >
      <form onSubmit={handleSubmit}>
        <TwoColumnForm>
          {/* Order ID and Status Row */}
          <FormField label={t('orderId')}>
            <IconInput
              icon={<Package className="w-4 h-4" />}
              value={orderId}
              readOnly
              className="bg-gray-50 text-gray-600 cursor-not-allowed"
            />
          </FormField>
          
          <FormField label={t('orderStatus')}>
            <IconInput
              icon={<Clock className="w-4 h-4" />}
              value={t('pending')}
              readOnly
              className="bg-gray-50 text-gray-600 cursor-not-allowed"
            />
          </FormField>

          {/* Order Type - Full width */}
          <FullWidthField>
            <FormField label={`${t('orderType')} *`}>
              <IconSelect
                icon={<Gift className="w-4 h-4" />}
                value={formData.orderType}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('orderType', e.target.value)}
                className={errors.orderType ? 'border-red-500' : ''}
              >
                <option value="">{t('selectOrderType')}...</option>
                {orderTypes.map((type) => (
                  <option key={type.key} value={type.display}>
                    {ordersT(`orderTypes.${type.key}`)}
                  </option>
                ))}
              </IconSelect>
              {errors.orderType && <p className="text-red-500 text-xs mt-1">{errors.orderType}</p>}
            </FormField>
          </FullWidthField>

          {/* Customer Information */}
          <FormField label={`${t('customerName')} *`}>
            <IconInput
              icon={<Users className="w-4 h-4" />}
              value={formData.customerName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('customerName', e.target.value)}
              className={errors.customerName ? 'border-red-500' : ''}
              placeholder={t('enterCustomerName')}
            />
            {errors.customerName && <p className="text-red-500 text-xs mt-1">{errors.customerName}</p>}
          </FormField>
          
          <FormField label={`${t('customerPhone')} *`}>
            <IconInput
              icon={<Phone className="w-4 h-4" />}
              value={formData.customerPhone}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('customerPhone', e.target.value)}
              className={errors.customerPhone ? 'border-red-500' : ''}
              placeholder={t('enterPhoneNumber')}
            />
            {errors.customerPhone && <p className="text-red-500 text-xs mt-1">{errors.customerPhone}</p>}
          </FormField>

          {/* Customer Address */}
          <FullWidthField>
            <FormField label={`${t('customerAddress')} *`}>
              <div className="relative">
                <textarea
                  value={formData.customerAddress}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleInputChange('customerAddress', e.target.value)}
                  rows={2}
                  className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 pl-9 text-sm resize-none ${
                    errors.customerAddress ? 'border-red-500' : ''
                  }`}
                  placeholder={t('enterCustomerAddress')}
                />
                <span className="absolute left-3 top-3 text-gray-400">
                  <MapPin className="w-4 h-4" />
                </span>
              </div>
              {errors.customerAddress && <p className="text-red-500 text-xs mt-1">{errors.customerAddress}</p>}
            </FormField>
          </FullWidthField>

          {/* Sales Rep and Delivery Agent */}
          <FormField label={`${t('salesRepresentative')} *`}>
            <IconSelect
              icon={<Users className="w-4 h-4" />}
              value={formData.salesRep}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('salesRep', e.target.value)}
              className={errors.salesRep ? 'border-red-500' : ''}
            >
              <option value="">{t('selectSalesRepresentative')}...</option>
              {Array.isArray(teamMembers) && teamMembers
                .filter(member => member.role === 'Sales')
                .map((member) => (
                  <option key={member.id} value={member.name}>
                    {member.name}
                  </option>
                ))}
            </IconSelect>
            {errors.salesRep && <p className="text-red-500 text-xs mt-1">{errors.salesRep}</p>}
          </FormField>
          
          <FormField label={`${t('deliveryAgent')} *`}>
            <IconSelect
              icon={<Truck className="w-4 h-4" />}
              value={formData.deliveryAgent}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('deliveryAgent', e.target.value)}
              className={errors.deliveryAgent ? 'border-red-500' : ''}
            >
              <option value="">{t('selectDeliveryAgent')}...</option>
              {Array.isArray(teamMembers) && teamMembers
                .filter(member => member.role === 'Delivery')
                .map((member) => (
                  <option key={member.id} value={member.name}>
                    {member.name}
                  </option>
                ))}
            </IconSelect>
            {errors.deliveryAgent && <p className="text-red-500 text-xs mt-1">{errors.deliveryAgent}</p>}
          </FormField>

          {/* Order Date and Estimated Delivery */}
          <FormField label={`${t('orderDate')} *`}>
            <IconInput
              icon={<Calendar className="w-4 h-4" />}
              type="date"
              value={formData.orderDate}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('orderDate', e.target.value)}
              className={errors.orderDate ? 'border-red-500' : ''}
            />
            {errors.orderDate && <p className="text-red-500 text-xs mt-1">{errors.orderDate}</p>}
          </FormField>
          
          <FormField label={`${t('estimatedDeliveryTime')} *`}>
            <IconInput
              icon={<Clock className="w-4 h-4" />}
              type="time"
              value={formData.estimatedDeliveryTime}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('estimatedDeliveryTime', e.target.value)}
              className={errors.estimatedDeliveryTime ? 'border-red-500' : ''}
            />
            {errors.estimatedDeliveryTime && <p className="text-red-500 text-xs mt-1">{errors.estimatedDeliveryTime}</p>}
          </FormField>

          {/* Deposit and Total Amount */}
          <FormField label={`${t('depositAmount')} *`}>
            <IconInput
              icon={<DollarSign className="w-4 h-4" />}
              type="number"
              value={formData.depositAmount}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('depositAmount', e.target.value)}
              className={errors.depositAmount ? 'border-red-500' : ''}
              placeholder="0.00"
            />
            {errors.depositAmount && <p className="text-red-500 text-xs mt-1">{errors.depositAmount}</p>}
          </FormField>
          
          <FormField label={`${t('totalOrderAmount')} *`}>
            <IconInput
              icon={<DollarSign className="w-4 h-4" />}
              type="number"
              value={formData.totalOrderAmount}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('totalOrderAmount', e.target.value)}
              className={errors.totalOrderAmount ? 'border-red-500' : ''}
              placeholder="0.00"
            />
            {errors.totalOrderAmount && <p className="text-red-500 text-xs mt-1">{errors.totalOrderAmount}</p>}
          </FormField>

          {/* Driver and Truck */}
          <FormField label={`${t('driver')} *`}>
            <IconSelect
              icon={<Users className="w-4 h-4" />}
              value={formData.driverId}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleDriverChange(e.target.value)}
              className={errors.driverId ? 'border-red-500' : ''}
            >
              <option value="">👤 {t('selectDriver')}...</option>
              {Array.isArray(drivers) && drivers
                .filter(driver => driver.status === 'Available' || driver.status === 'On Duty')
                .map((driver) => (
                  <option key={driver.id} value={driver.id}>
                    {driver.name}
                  </option>
                ))}
            </IconSelect>
            {formData.driverId && (
              <p className="text-xs text-gray-600 mt-1">
                {getDriverAssignmentInfo(formData.driverId)}
              </p>
            )}
            {errors.driverId && <p className="text-red-500 text-xs mt-1">{errors.driverId}</p>}
          </FormField>
          
          <FormField label={`${t('truck')} *`}>
            <IconSelect
              icon={<Truck className="w-4 h-4" />}
              value={formData.truckId}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange('truckId', e.target.value)}
              disabled={!formData.driverId}
              className={errors.truckId ? 'border-red-500' : ''}
            >
              <option value="">
                {!formData.driverId
                  ? `${t('selectDriverFirst')}...`
                  : `${t('truck')}...`
                }
              </option>
              {getAvailableTrucks().map((truck) => (
                <option key={truck.id} value={truck.id}>
                  {truck.licensePlate} - {truck.model}
                </option>
              ))}
            </IconSelect>
            {!formData.driverId && (
              <p className="text-xs text-gray-500 mt-1">
                {t('selectDriverToSeeAvailableTrucks')}
              </p>
            )}
            {formData.driverId && getAvailableTrucks().length === 0 && (
              <p className="text-xs text-orange-600 mt-1">
                {t('noTrucksAvailableForDriver')}
              </p>
            )}
            {errors.truckId && <p className="text-red-500 text-xs mt-1">{errors.truckId}</p>}
          </FormField>
        </TwoColumnForm>

        <ModalActions
          onCancel={onClose}
          cancelText={t('cancel')}
          submitText={editOrder ? t('updateOrder') : t('createOrder')}
          icon={editOrder ? <Edit className="w-4 h-4" /> : <Plus className="w-4 h-4" />}
          cancelIcon={<X className="w-4 h-4" />}
        />
      </form>
    </CompactModal>
  );
};

// Main Orders Page Component
const OrdersPage: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const t = useOrdersTranslations();

  const [activeItem, setActiveItem] = useState('orders');
  const [allOrders, setAllOrders] = useState<Order[]>([]);
  const [statsLoading, setStatsLoading] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const [showSettings, setShowSettings] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [showSupplierPaymentModal, setShowSupplierPaymentModal] = useState(false);
  const [showCustomerPaymentModal, setShowCustomerPaymentModal] = useState(false);
  const [showRescheduleModal, setShowRescheduleModal] = useState(false);
  const [showReassignModal, setShowReassignModal] = useState(false);
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [showQuickOrderModal, setShowQuickOrderModal] = useState(false);
  const [editOrder, setEditOrder] = useState<Order | null>(null);
  const [pendingStatusUpdate, setPendingStatusUpdate] = useState<{
    orderId: string;
    newStatus: OrderStatus;
    actionLog?: ActionLog;
  } | null>(null);
  const [expandedStages, setExpandedStages] = useState<{[key: string]: boolean}>({});

  // Helper function to get status translation
  const getStatusTranslation = (status: OrderStatus) => {
    const statusMap: Record<OrderStatus, string> = {
      'Pending': t('status.pending'),
      'Started': t('status.started'),
      'Moved to Supplier': t('status.movedToSupplier'),
      'Arrived at Supplier': t('status.arrivedAtSupplier'),
      'Moving to Customer': t('status.movingToCustomer'),
      'Arrived at Customer': t('status.arrivedAtCustomer'),
      'Delivered': t('status.delivered'),
      'Cancelled': t('status.cancelled')
    };
    return statusMap[status] || status;
  };

  // Load all orders for statistics
  const loadAllOrders = async () => {
    try {
      setStatsLoading(true);
      const allOrdersData = await Promise.all([
        fetchOrdersByTab('oncoming'),
        fetchOrdersByTab('ongoing'),
        fetchOrdersByTab('completed'),
        fetchOrdersByTab('cancelled')
      ]);
      const combinedOrders = allOrdersData.flat();
      setAllOrders(combinedOrders);
    } catch (error) {
      console.error('Error loading all orders:', error);
      setAllOrders([]);
    } finally {
      setStatsLoading(false);
    }
  };



  // Navigation transition hook
  const { navigateWithTransition } = useNavigationTransition({ animationType: 'fade' });

  // Handle navigation
  const handleNavigation = (path: string) => {
    navigateWithTransition(path);
  };

  // Define table columns for responsive table
  const orderColumns = [
    { key: 'id', label: t('orderId'), mobileLabel: 'ID' },
    { key: 'customer', label: t('customer') },
    { key: 'type', label: t('orderType'), hideOnMobile: true },
    { key: 'status', label: t('common.status') },
    { key: 'date', label: t('date'), hideOnMobile: true },
    { key: 'amount', label: t('amount') },
    { key: 'deliveryAgent', label: t('deliveryAgent'), hideOnMobile: true }
  ];

  // Render desktop table row
  const renderOrderRow = (order: Order) => (
    <ResponsiveTableRow
      key={order.id}
      onClick={() => handleOrderClick(order)}
      className="cursor-pointer"
    >
      <ResponsiveTableCell className="font-medium text-indigo-600 group-hover:text-indigo-700">
        {order.id}
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
            {order.avatar ? (
              <Image src={order.avatar} alt={order.customer} width={40} height={40} className="w-full h-full rounded-full object-cover" />
            ) : (
              order.customer.charAt(0)
            )}
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">{order.customer}</p>
            <p className="text-xs text-gray-500">{order.phone}</p>
          </div>
        </div>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <span className="text-sm text-gray-600">{order.orderType}</span>
      </ResponsiveTableCell>
      <ResponsiveTableCell>
        <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getStatusClass(order.status)}`}>
          {getStatusTranslation(order.status)}
        </span>
      </ResponsiveTableCell>
      <ResponsiveTableCell className="text-gray-500">
        {order.date}
      </ResponsiveTableCell>
      <ResponsiveTableCell className="font-semibold text-gray-900">
        {order.totalOrderAmount} EGP
      </ResponsiveTableCell>
      <ResponsiveTableCell className="text-gray-600">
        {order.deliveryAgent}
      </ResponsiveTableCell>
    </ResponsiveTableRow>
  );

  // Render mobile card
  const renderOrderMobileCard = (order: Order) => (
    <div
      key={order.id}
      className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow cursor-pointer"
      onClick={() => handleOrderClick(order)}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center text-white font-bold">
            {order.avatar ? (
              <Image src={order.avatar} alt={order.customer} width={48} height={48} className="w-full h-full rounded-full object-cover" />
            ) : (
              order.customer.charAt(0)
            )}
          </div>
          <div>
            <p className="font-medium text-gray-900">{order.customer}</p>
            <p className="text-sm text-indigo-600 font-medium">{order.id}</p>
          </div>
        </div>
        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(order.status)}`}>
          {getStatusTranslation(order.status)}
        </span>
      </div>
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span className="text-gray-500">{t('amount')}:</span>
          <span className="ml-1 font-semibold">{order.totalOrderAmount} EGP</span>
        </div>
        <div>
          <span className="text-gray-500">{t('deliveryAgent')}:</span>
          <span className="ml-1">{order.deliveryAgent}</span>
        </div>
      </div>
    </div>
  );

  // Handle order click
  const handleOrderClick = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
    // Initialize all stages as expanded (unfolded) by default for this order
    if (!expandedStages[order.id]) {
      const allStatuses: OrderStatus[] = [
        'Pending', 'Started', 'Moved to Supplier', 'Arrived at Supplier',
        'Moving to Customer', 'Arrived at Customer', 'Delivered'
      ];
      const initialExpanded = allStatuses.reduce((acc, status) => {
        acc[`${order.id}-${status}`] = true; // All stages unfolded by default
        return acc;
      }, {} as {[key: string]: boolean});

      setExpandedStages(prev => ({ ...prev, ...initialExpanded }));
    }
  };

  // Toggle stage expansion
  const toggleStageExpansion = (orderId: string, status: OrderStatus) => {
    const key = `${orderId}-${status}`;
    setExpandedStages(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Close order modal
  const closeOrderModal = () => {
    setShowOrderModal(false);
    setSelectedOrder(null);
  };

  // Handle quick order save
  const handleQuickOrderSave = async (orderData: QuickOrderFormData) => {
    try {
      // Get current location for the order creation action
      const location = await getCurrentLocation();

      // Create order object in the format expected by the API
      const orderPayload = {
        customerName: orderData.customerName,
        customerAddress: orderData.customerAddress,
        customerPhone: orderData.customerPhone,
        orderType: orderData.orderType,
        totalAmount: parseFloat(orderData.totalOrderAmount),
        depositAmount: parseFloat(orderData.depositAmount),
        salesRep: orderData.salesRep,
        deliveryAgent: orderData.deliveryAgent,
        estimatedDeliveryTime: orderData.estimatedDeliveryTime,
        driverId: orderData.driverId,
        driverName: orderData.driverName || '',
        truckId: orderData.truckId,
        truckLicensePlate: orderData.truckLicensePlate || '',
        items: [], // Empty items array for quick orders
        // Include user and location information for the initial action
        createdBy: user?.displayName || user?.email || 'Unknown User',
        createdByUserId: user?.uid || 'unknown',
        creationLocation: location
      };

      // Save order to Firestore via API
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orderPayload)
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to create order');
      }

      // Refresh all orders to get the latest data from Firestore
      await loadAllOrders();

      console.log('New order created successfully:', result.data);
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  };

  // Handle order edit - opens the edit modal
  const handleOrderEdit = (order: Order) => {
    setEditOrder(order);
    setShowQuickOrderModal(true);
    setShowOrderModal(false); // Close the details modal
  };

  // Handle order update - saves the edited order
  const handleOrderUpdate = async (orderData: QuickOrderFormData) => {
    if (!editOrder) return;

    try {
      // Create update payload
      const updatePayload = {
        customer: orderData.customerName,
        phone: orderData.customerPhone,
        address: orderData.customerAddress,
        amount: `EGP ${parseFloat(orderData.totalOrderAmount).toLocaleString()}`,
        salesRep: orderData.salesRep,
        deliveryAgent: orderData.deliveryAgent,
        estimatedDeliveryTime: orderData.estimatedDeliveryTime,
        depositAmount: orderData.depositAmount,
        driverId: orderData.driverId,
        truckId: orderData.truckId,
        driverName: orderData.driverName || '',
        truckLicensePlate: orderData.truckLicensePlate || ''
      };

      // Update order via API
      const response = await fetch(`/api/orders/${editOrder.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatePayload)
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to update order');
      }

      // Update the local state
      setAllOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === editOrder.id
            ? { ...order, ...updatePayload }
            : order
        )
      );

      // Clear edit state
      setEditOrder(null);

      console.log('Order updated successfully:', result.data);
    } catch (error) {
      console.error('Error updating order:', error);
      throw error;
    }
  };

  // Handle status update
  const handleStatusUpdate = async (
    orderId: string,
    newStatus: OrderStatus,
    actionLog?: ActionLog,
    paymentInfo?: Partial<PaymentInfo>
  ) => {
    try {
      // Check if this status change requires payment confirmation
      if (newStatus === 'Moving to Customer' && selectedOrder?.status === 'Arrived at Supplier') {
        // Show supplier payment modal
        setPendingStatusUpdate({ orderId, newStatus, actionLog });
        setShowSupplierPaymentModal(true);
        return;
      }

      if (newStatus === 'Delivered' && selectedOrder?.status === 'Arrived at Customer') {
        // Show customer payment modal
        setPendingStatusUpdate({ orderId, newStatus, actionLog });
        setShowCustomerPaymentModal(true);
        return;
      }

      // For other status changes, proceed directly
      const location = await getCurrentLocation();
      const finalActionLog: ActionLog = actionLog || {
        id: `action-${Date.now()}`,
        timestamp: new Date().toLocaleString(),
        status: newStatus,
        location: location.address,
        gpsCoordinates: { lat: location.lat, lng: location.lng },
        userId: user?.uid || 'unknown',
        userName: user?.displayName || user?.email || 'Unknown User',
        notes: `Status updated to ${newStatus}`
      };

      const updatedOrder = await updateOrderStatus(orderId, newStatus, finalActionLog, paymentInfo);

      if (updatedOrder) {
        // Refresh all orders
        await loadAllOrders();

        // Update selected order if it's the same one
        if (selectedOrder?.id === orderId) {
          setSelectedOrder(updatedOrder);
        }

        // Close modal if status is final
        if (newStatus === 'Delivered' || newStatus === 'Cancelled') {
          closeOrderModal();
        }
      }
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  };

  // Handle supplier payment confirmation
  const handleSupplierPaymentConfirm = async (amount: string) => {
    if (!pendingStatusUpdate) {
      console.error('No pending status update found');
      return;
    }

    console.log('Confirming supplier payment:', {
      amount,
      orderId: pendingStatusUpdate.orderId,
      newStatus: pendingStatusUpdate.newStatus
    });

    try {
      const location = await getCurrentLocation();
      const actionLog: ActionLog = {
        id: `action-${Date.now()}`,
        timestamp: new Date().toLocaleString(),
        status: pendingStatusUpdate.newStatus,
        location: location.address,
        gpsCoordinates: { lat: location.lat, lng: location.lng },
        userId: user?.uid || 'unknown',
        userName: user?.displayName || user?.email || 'Unknown User',
        notes: `Supplier payment confirmed: ${amount}`
      };

      const paymentInfo: Partial<PaymentInfo> = {
        supplierPayment: {
          amount,
          timestamp: new Date().toLocaleString(),
          confirmedBy: user?.displayName || user?.email || 'Unknown User'
        }
      };

      console.log('Updating order status with:', { actionLog, paymentInfo });

      // Directly update the order status without going through handleStatusUpdate to avoid recursion
      const updatedOrder = await updateOrderStatus(
        pendingStatusUpdate.orderId,
        pendingStatusUpdate.newStatus,
        actionLog,
        paymentInfo
      );

      console.log('Order update result:', updatedOrder);

      if (updatedOrder) {
        console.log('Refreshing all orders');
        // Refresh all orders
        await loadAllOrders();

        // Update selected order if it's the same one
        if (selectedOrder?.id === pendingStatusUpdate.orderId) {
          setSelectedOrder(updatedOrder);
          console.log('Updated selected order');
        }
      } else {
        console.error('Failed to update order');
      }

      setPendingStatusUpdate(null);
      setShowSupplierPaymentModal(false);
    } catch (error) {
      console.error('Error confirming supplier payment:', error);
      setPendingStatusUpdate(null);
      setShowSupplierPaymentModal(false);
    }
  };

  // Handle customer payment confirmation
  const handleCustomerPaymentConfirm = async (paymentData: {
    amount: string;
    method: string;
    deliveryStatus: string
  }) => {
    if (!pendingStatusUpdate) return;

    try {
      const location = await getCurrentLocation();
      const actionLog: ActionLog = {
        id: `action-${Date.now()}`,
        timestamp: new Date().toLocaleString(),
        status: pendingStatusUpdate.newStatus,
        location: location.address,
        gpsCoordinates: { lat: location.lat, lng: location.lng },
        userId: user?.uid || 'unknown',
        userName: user?.displayName || user?.email || 'Unknown User',
        notes: `Order delivered - Payment: ${paymentData.amount} via ${paymentData.method}`
      };

      const paymentInfo: Partial<PaymentInfo> = {
        customerPayment: {
          amount: paymentData.amount,
          method: paymentData.method as 'Cash' | 'Card' | 'Digital' | 'Other',
          deliveryStatus: paymentData.deliveryStatus as 'Full' | 'Partial',
          timestamp: new Date().toLocaleString(),
          receivedBy: user?.displayName || user?.email || 'Unknown User'
        }
      };

      // Directly update the order status without going through handleStatusUpdate to avoid recursion
      const updatedOrder = await updateOrderStatus(
        pendingStatusUpdate.orderId,
        pendingStatusUpdate.newStatus,
        actionLog,
        paymentInfo
      );

      if (updatedOrder) {
        // Refresh all orders
        await loadAllOrders();

        // Close modal since order is now delivered
        closeOrderModal();
      }

      setPendingStatusUpdate(null);
      setShowCustomerPaymentModal(false);
    } catch (error) {
      console.error('Error confirming customer payment:', error);
      setPendingStatusUpdate(null);
      setShowCustomerPaymentModal(false);
    }
  };

  // Handle order reschedule
  const handleReschedule = async (orderId: string, newDate: string) => {
    try {
      const location = await getCurrentLocation();
      const actionLog: ActionLog = {
        id: `action-${Date.now()}`,
        timestamp: new Date().toLocaleString(),
        status: selectedOrder?.status || 'Pending',
        location: location.address,
        gpsCoordinates: { lat: location.lat, lng: location.lng },
        userId: user?.uid || 'unknown',
        userName: user?.displayName || user?.email || 'Unknown User',
        notes: `Order rescheduled to ${newDate}`
      };

      const updatedOrder = await updateOrderStatus(orderId, selectedOrder?.status || 'Pending', actionLog, undefined, newDate);

      if (updatedOrder) {
        await loadAllOrders();
        if (selectedOrder?.id === orderId) {
          setSelectedOrder(updatedOrder);
        }
      }
    } catch (error) {
      console.error('Error rescheduling order:', error);
    }
  };

  // Handle order cancellation
  const handleCancel = async (orderId: string, reason: string, notes?: string) => {
    try {
      const location = await getCurrentLocation();
      const actionLog: ActionLog = {
        id: `action-${Date.now()}`,
        timestamp: new Date().toLocaleString(),
        status: 'Cancelled',
        location: location.address,
        gpsCoordinates: { lat: location.lat, lng: location.lng },
        userId: user?.uid || 'unknown',
        userName: user?.displayName || user?.email || 'Unknown User',
        notes: notes ? `Order cancelled: ${reason}. ${notes}` : `Order cancelled: ${reason}`
      };

      // Update order with cancellation reason
      const updatedOrder = await updateOrderStatus(orderId, 'Cancelled', actionLog, undefined, undefined, undefined, reason);

      if (updatedOrder) {
        await loadAllOrders();
        closeOrderModal();
      }
    } catch (error) {
      console.error('Error cancelling order:', error);
    }
  };

  // Handle delivery agent re-assignment
  const handleReassign = async (orderId: string, newDeliveryAgent: string) => {
    try {
      const location = await getCurrentLocation();
      const actionLog: ActionLog = {
        id: `action-${Date.now()}`,
        timestamp: new Date().toLocaleString(),
        status: selectedOrder?.status || 'Pending',
        location: location.address,
        gpsCoordinates: { lat: location.lat, lng: location.lng },
        userId: user?.uid || 'unknown',
        userName: user?.displayName || user?.email || 'Unknown User',
        notes: `Delivery agent re-assigned to ${newDeliveryAgent}`
      };

      // Update the order with new delivery agent
      const updatedOrder = await updateOrderStatus(
        orderId,
        selectedOrder?.status || 'Pending',
        actionLog,
        undefined,
        undefined,
        newDeliveryAgent
      );

      if (updatedOrder) {
        await loadAllOrders();
        if (selectedOrder?.id === orderId) {
          setSelectedOrder(updatedOrder);
        }
      }
    } catch (error) {
      console.error('Error re-assigning delivery agent:', error);
    }
  };

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  // Load initial orders
  useEffect(() => {
    if (user) {
      loadAllOrders(); // Load all orders
    }
  }, [user]);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showUserMenu) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu]);

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <PageTransition transitionKey="orders" animationType="fade">
      <div className="h-screen bg-gray-50 text-gray-900" data-page-content>
        <Sidebar
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          onNavigate={handleNavigation}
          onOpenSettings={() => setShowSettings(true)}
        />
        <main className="lg:ml-64 flex flex-col h-full overflow-hidden">
          <Header
            showUserMenu={showUserMenu}
            setShowUserMenu={setShowUserMenu}
            setActiveItem={setActiveItem}
            setShowSettings={setShowSettings}
          />
          <div className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-8 pt-25">
            {/* Creative Add New Order Section */}
            <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-3xl p-8 mb-8 text-white relative overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-4 left-4 w-20 h-20 border-2 border-white rounded-full"></div>
                <div className="absolute top-12 right-8 w-16 h-16 border-2 border-white rounded-full"></div>
                <div className="absolute bottom-8 left-12 w-12 h-12 border-2 border-white rounded-full"></div>
                <div className="absolute bottom-4 right-16 w-8 h-8 border-2 border-white rounded-full"></div>
              </div>

              <div className="relative z-10 flex flex-col md:flex-row items-center justify-between">
                <div className="mb-6 md:mb-0">
                  <h2 className="text-3xl font-bold mb-2">{t('readyToCreate')}</h2>
                  <p className="text-lg opacity-90 mb-4">{t('startAdding')}</p>
                  <div className="flex items-center gap-4 text-sm opacity-80">
                    <div className="flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      <span>{t('orderManagement')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Truck className="w-4 h-4" />
                      <span>{t('deliveryTracking')}</span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button
                    onClick={() => setShowQuickOrderModal(true)}
                    className="bg-white text-indigo-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-50 transition duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center gap-3"
                  >
                    <Package className="w-6 h-6" />
                    {t('newOrder')}
                  </button>
                  <button className="bg-white/20 backdrop-blur-sm border-2 border-white text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white/30 transition duration-300 flex items-center gap-3">
                    <Clock className="w-6 h-6" />
                    {t('bulkExport')}
                  </button>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('title')}</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {statsLoading ? '...' : allOrders.length}
                    </p>
                  </div>
                  <Package className="w-8 h-8 text-indigo-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('oncomingOrders')}</p>
                    <p className="text-3xl font-bold text-blue-600">
                      {statsLoading ? '...' : allOrders.filter(order => order.status === 'Pending').length}
                    </p>
                  </div>
                  <Clock className="w-8 h-8 text-blue-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('ongoingOrders')}</p>
                    <p className="text-3xl font-bold text-orange-600">
                      {statsLoading ? '...' : allOrders.filter(order =>
                        ['Started', 'Moved to Supplier', 'Arrived at Supplier', 'Moving to Customer', 'Arrived at Customer'].includes(order.status)
                      ).length}
                    </p>
                  </div>
                  <Truck className="w-8 h-8 text-orange-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('completedOrders')}</p>
                    <p className="text-3xl font-bold text-green-600">
                      {statsLoading ? '...' : allOrders.filter(order => order.status === 'Delivered').length}
                    </p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{t('cancelledOrders')}</p>
                    <p className="text-3xl font-bold text-red-600">
                      {statsLoading ? '...' : allOrders.filter(order => order.status === 'Cancelled').length}
                    </p>
                  </div>
                  <XCircle className="w-8 h-8 text-red-600" />
                </div>
              </div>
            </div>

            {/* All Orders Table */}
            <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
              {/* Header */}
              <div className="p-4 sm:p-6 border-b border-gray-100">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <Package className="w-6 h-6 text-gray-600" />
                    <h3 className="text-lg font-semibold text-gray-900">{t('title')}</h3>
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
                      {statsLoading ? '...' : allOrders.length}
                    </span>
                  </div>

                  {/* Search and Filters */}
                  <div className="flex items-center gap-4">
                    <div className="flex flex-wrap gap-2">
                      {(['All', 'Pending', 'Started', 'Delivered', 'Cancelled'] as const).map((status) => (
                        <button
                          key={status}
                          className={`px-3 py-1 rounded-lg font-medium text-xs transition duration-200 ${
                            status === 'All'
                              ? 'bg-indigo-600 text-white shadow-lg'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {status === 'All' ? t('all') :
                           status === 'Pending' ? t('pending') :
                           status === 'Started' ? t('started') :
                           status === 'Delivered' ? t('delivered') :
                           status === 'Cancelled' ? t('cancelled') : status} {status !== 'All' && `(${status === 'Pending' ? allOrders.filter(order => order.status === 'Pending').length :
                            status === 'Started' ? allOrders.filter(order => ['Started', 'Moved to Supplier', 'Arrived at Supplier', 'Moving to Customer', 'Arrived at Customer'].includes(order.status)).length :
                            status === 'Delivered' ? allOrders.filter(order => order.status === 'Delivered').length :
                            status === 'Cancelled' ? allOrders.filter(order => order.status === 'Cancelled').length : 0})`}
                        </button>
                      ))}
                    </div>

                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder={t('searchOrders')}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 w-64 text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Main Content Table */}
              <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
                <ResponsiveTable
                  columns={orderColumns}
                  data={allOrders}
                  renderRow={renderOrderRow}
                  renderMobileCard={renderOrderMobileCard}
                  loading={statsLoading}
                  emptyState={
                    <div className="flex flex-col items-center py-12">
                      <Package className="w-16 h-16 text-gray-300 mb-4" />
                      <p className="text-lg font-medium text-gray-500 mb-2">{t('noOrdersFound')}</p>
                      <p className="text-sm text-gray-400">{t('ordersWillAppearHere')}</p>
                    </div>
                  }
                />
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* Order Details Modal */}
      <OrderDetailsModal
        order={selectedOrder}
        isOpen={showOrderModal}
        onClose={closeOrderModal}
        onStatusUpdate={handleStatusUpdate}
        onEdit={handleOrderEdit}
        setShowRescheduleModal={setShowRescheduleModal}
        setShowReassignModal={setShowReassignModal}
        setShowCancellationModal={setShowCancellationModal}
        expandedStages={expandedStages}
        toggleStageExpansion={toggleStageExpansion}
        getStatusTranslation={getStatusTranslation}
      />

      {/* Payment Confirmation Modals */}
      <SupplierPaymentModal
        isOpen={showSupplierPaymentModal}
        onClose={() => {
          setShowSupplierPaymentModal(false);
          setPendingStatusUpdate(null);
        }}
        onConfirm={handleSupplierPaymentConfirm}
      />

      <CustomerPaymentModal
        isOpen={showCustomerPaymentModal}
        onClose={() => {
          setShowCustomerPaymentModal(false);
          setPendingStatusUpdate(null);
        }}
        onConfirm={handleCustomerPaymentConfirm}
      />

      <RescheduleModal
        isOpen={showRescheduleModal}
        onClose={() => setShowRescheduleModal(false)}
        onConfirm={(newDate) => {
          if (selectedOrder) {
            handleReschedule(selectedOrder.id, newDate);
          }
          setShowRescheduleModal(false);
        }}
      />

      <ReassignModal
        isOpen={showReassignModal}
        onClose={() => setShowReassignModal(false)}
        currentAgent={selectedOrder?.deliveryAgent}
        onConfirm={(newAgent) => {
          if (selectedOrder) {
            handleReassign(selectedOrder.id, newAgent);
          }
          setShowReassignModal(false);
        }}
      />

      <CancellationModal
        isOpen={showCancellationModal}
        onClose={() => setShowCancellationModal(false)}
        onConfirm={(reason, notes) => {
          if (selectedOrder) {
            handleCancel(selectedOrder.id, reason, notes);
          }
          setShowCancellationModal(false);
        }}
      />

      {/* Quick Order Modal */}
      <QuickOrderModal
        isOpen={showQuickOrderModal}
        onClose={() => {
          setShowQuickOrderModal(false);
          setEditOrder(null); // Clear edit state when closing
        }}
        onSave={editOrder ? handleOrderUpdate : handleQuickOrderSave}
        editOrder={editOrder}
      />

      {/* Settings Modal */}
      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </PageTransition>
  );
};

export default OrdersPage;

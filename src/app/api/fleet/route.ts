import { NextRequest, NextResponse } from 'next/server';

// Fleet types
type TruckStatus = 'Active' | 'Maintenance' | 'Inactive';
type DriverStatus = 'Available' | 'On Duty' | 'Off Duty' | 'Inactive';

type Truck = {
  id: string;
  licensePlate: string;
  model: string;
  year: number;
  capacity: string;
  status: TruckStatus;
  driverId?: string;
  driverName?: string;
  lastMaintenance: string;
  nextMaintenance: string;
  mileage: number;
  fuelType: 'Diesel' | 'Gasoline' | 'Electric';
};

type Driver = {
  id: string;
  name: string;
  licenseNumber: string;
  phone: string;
  email: string;
  status: DriverStatus;
  truckId?: string;
  truckLicensePlate?: string;
  joinDate: string;
  licenseExpiry: string;
  experience: number; // years
};

// Fleet data - starts empty
const trucks: Truck[] = [];
const drivers: Driver[] = [];

// GET - Fetch fleet data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'trucks', 'drivers', or 'all'
    const status = searchParams.get('status');

    const response: Record<string, unknown> = {};

    if (type === 'trucks' || !type) {
      let filteredTrucks = trucks;
      if (status && status !== 'All') {
        filteredTrucks = filteredTrucks.filter(truck => truck.status === status);
      }
      response.trucks = filteredTrucks;
    }

    if (type === 'drivers' || !type) {
      let filteredDrivers = drivers;
      if (status && status !== 'All') {
        filteredDrivers = filteredDrivers.filter(driver => driver.status === status);
      }
      response.drivers = filteredDrivers;
    }

    return NextResponse.json({
      success: true,
      data: response,
      message: 'Fleet data retrieved successfully'
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to fetch fleet data' },
      { status: 500 }
    );
  }
}

// POST - Add new truck or driver
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, ...data } = body;

    if (type === 'truck') {
      const { licensePlate, model, year, capacity, status, fuelType, lastMaintenance, nextMaintenance, mileage } = data;

      // Validate required fields
      if (!licensePlate || !model || !year || !capacity || !fuelType) {
        return NextResponse.json(
          { success: false, error: 'Missing required truck fields' },
          { status: 400 }
        );
      }

      // Check if license plate already exists
      const existingTruck = trucks.find(truck => truck.licensePlate === licensePlate);
      if (existingTruck) {
        return NextResponse.json(
          { success: false, error: 'License plate already exists' },
          { status: 400 }
        );
      }

      // Create new truck
      const newTruck: Truck = {
        id: `truck-${Date.now()}`,
        licensePlate,
        model,
        year: parseInt(year),
        capacity,
        status: status || 'Active',
        lastMaintenance: lastMaintenance || new Date().toISOString().split('T')[0],
        nextMaintenance: nextMaintenance || new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        mileage: mileage || 0,
        fuelType
      };

      trucks.push(newTruck);

      return NextResponse.json({
        success: true,
        data: newTruck,
        message: 'Truck added successfully'
      });

    } else if (type === 'driver') {
      const { name, licenseNumber, phone, email, status, joinDate, licenseExpiry, experience } = data;

      // Validate required fields
      if (!name || !licenseNumber || !phone || !email) {
        return NextResponse.json(
          { success: false, error: 'Missing required driver fields' },
          { status: 400 }
        );
      }

      // Check if license number or email already exists
      const existingDriver = drivers.find(driver =>
        driver.licenseNumber === licenseNumber || driver.email === email
      );
      if (existingDriver) {
        return NextResponse.json(
          { success: false, error: 'License number or email already exists' },
          { status: 400 }
        );
      }

      // Create new driver
      const newDriver: Driver = {
        id: `driver-${Date.now()}`,
        name,
        licenseNumber,
        phone,
        email,
        status: status || 'Available',
        joinDate: joinDate || new Date().toISOString().split('T')[0],
        licenseExpiry: licenseExpiry || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        experience: experience || 0
      };

      drivers.push(newDriver);

      return NextResponse.json({
        success: true,
        data: newDriver,
        message: 'Driver added successfully'
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid type specified' },
      { status: 400 }
    );

  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to add fleet item' },
      { status: 500 }
    );
  }
}

// PUT - Update truck or driver
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'ID is required' },
        { status: 400 }
      );
    }

    if (type === 'truck') {
      const truckIndex = trucks.findIndex(truck => truck.id === id);
      if (truckIndex === -1) {
        return NextResponse.json(
          { success: false, error: 'Truck not found' },
          { status: 404 }
        );
      }

      // Check if license plate already exists (excluding current truck)
      if (updateData.licensePlate) {
        const existingTruck = trucks.find(truck =>
          truck.licensePlate === updateData.licensePlate && truck.id !== id
        );
        if (existingTruck) {
          return NextResponse.json(
            { success: false, error: 'License plate already exists' },
            { status: 400 }
          );
        }
      }

      // Update truck
      const updatedTruck = {
        ...trucks[truckIndex],
        ...updateData,
        ...(updateData.year && { year: parseInt(updateData.year) }),
        ...(updateData.mileage && { mileage: parseInt(updateData.mileage) })
      };

      trucks[truckIndex] = updatedTruck;

      return NextResponse.json({
        success: true,
        data: updatedTruck,
        message: 'Truck updated successfully'
      });

    } else if (type === 'driver') {
      const driverIndex = drivers.findIndex(driver => driver.id === id);
      if (driverIndex === -1) {
        return NextResponse.json(
          { success: false, error: 'Driver not found' },
          { status: 404 }
        );
      }

      // Check if license number or email already exists (excluding current driver)
      if (updateData.licenseNumber || updateData.email) {
        const existingDriver = drivers.find(driver =>
          (updateData.licenseNumber && driver.licenseNumber === updateData.licenseNumber && driver.id !== id) ||
          (updateData.email && driver.email === updateData.email && driver.id !== id)
        );
        if (existingDriver) {
          return NextResponse.json(
            { success: false, error: 'License number or email already exists' },
            { status: 400 }
          );
        }
      }

      // Update driver
      const updatedDriver = {
        ...drivers[driverIndex],
        ...updateData,
        ...(updateData.experience && { experience: parseInt(updateData.experience) })
      };

      drivers[driverIndex] = updatedDriver;

      return NextResponse.json({
        success: true,
        data: updatedDriver,
        message: 'Driver updated successfully'
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid type specified' },
      { status: 400 }
    );

  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to update fleet item' },
      { status: 500 }
    );
  }
}

// DELETE - Remove truck or driver
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, id } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'ID is required' },
        { status: 400 }
      );
    }

    if (type === 'truck') {
      const truckIndex = trucks.findIndex(truck => truck.id === id);
      if (truckIndex === -1) {
        return NextResponse.json(
          { success: false, error: 'Truck not found' },
          { status: 404 }
        );
      }

      const deletedTruck = trucks[truckIndex];
      trucks.splice(truckIndex, 1);

      // Remove truck assignment from drivers
      drivers.forEach(driver => {
        if (driver.truckId === id) {
          driver.truckId = undefined;
          driver.truckLicensePlate = undefined;
          driver.status = 'Available';
        }
      });

      return NextResponse.json({
        success: true,
        data: deletedTruck,
        message: 'Truck deleted successfully'
      });

    } else if (type === 'driver') {
      const driverIndex = drivers.findIndex(driver => driver.id === id);
      if (driverIndex === -1) {
        return NextResponse.json(
          { success: false, error: 'Driver not found' },
          { status: 404 }
        );
      }

      const deletedDriver = drivers[driverIndex];
      drivers.splice(driverIndex, 1);

      // Remove driver assignment from trucks
      trucks.forEach(truck => {
        if (truck.driverId === id) {
          truck.driverId = undefined;
          truck.driverName = undefined;
        }
      });

      return NextResponse.json({
        success: true,
        data: deletedDriver,
        message: 'Driver deleted successfully'
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid type specified' },
      { status: 400 }
    );

  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to delete fleet item' },
      { status: 500 }
    );
  }
}

import { NextResponse } from 'next/server';
import { testEmailNotification } from '@/utils/testEmail';

/**
 * Test API endpoint for email notifications
 * This endpoint is for development/testing purposes only
 * 
 * Usage: POST /api/test-email
 */
export async function POST() {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, error: 'Test endpoint not available in production' },
        { status: 403 }
      );
    }

    console.log('🧪 Testing email notifications via API...');
    
    const result = await testEmailNotification();
    
    return NextResponse.json({
      success: true,
      message: 'Email test completed',
      data: result
    });
  } catch (error) {
    console.error('❌ Email test API failed:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Email test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Email test endpoint. Use POST method to test email notifications.',
    usage: 'POST /api/test-email',
    note: 'Only available in development environment'
  });
}

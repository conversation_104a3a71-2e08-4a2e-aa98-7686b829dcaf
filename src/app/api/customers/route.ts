import { NextRequest, NextResponse } from 'next/server';
import { CustomersService } from '@/services/firebase/customers';
// GET - Fetch customers
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let customers;

    // Get customers based on filters
    if (status && status !== 'All') {
      customers = await CustomersService.getCustomersByStatus(status as 'Active' | 'Inactive');
    } else {
      customers = await CustomersService.getAllCustomers();
    }

    // Filter by search term if specified
    if (search) {
      const searchLower = search.toLowerCase();
      customers = customers.filter(customer =>
        customer.name.toLowerCase().includes(searchLower) ||
        customer.email.toLowerCase().includes(searchLower) ||
        customer.phone.includes(search) ||
        customer.address.toLowerCase().includes(searchLower)
      );
    }

    // Format customers for response
    const formattedCustomers = customers.map(customer => ({
      id: customer.id,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      address: customer.address,
      avatar: customer.avatar || null,
      status: customer.status,
      isVip: customer.isVIP,
      joinDate: customer.joinDate.toDate().toISOString().split('T')[0],
      totalOrders: customer.totalOrders,
      totalSpent: `${customer.totalSpent} EGP`,
      lastOrderDate: customer.lastOrderDate?.toDate().toISOString().split('T')[0],
      notes: customer.notes,
      preferredPaymentMethod: customer.preferredPaymentMethod,
      location: customer.location
    }));

    return NextResponse.json({
      success: true,
      data: formattedCustomers,
      message: 'Customers retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch customers' },
      { status: 500 }
    );
  }
}

// POST - Add new customer
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, phone, address, status, isVip, preferredPaymentMethod, notes, location } = body;

    // Validate required fields
    if (!name || !email || !phone || !address) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingCustomer = await CustomersService.getCustomerByEmail(email);
    if (existingCustomer) {
      return NextResponse.json(
        { success: false, error: 'Email already exists' },
        { status: 400 }
      );
    }

    // Create new customer
    const newCustomer = {
      name,
      email,
      phone,
      address,
      status: status || 'Active',
      isVIP: isVip || false,
      totalOrders: 0,
      totalSpent: 0,
      preferredPaymentMethod,
      notes,
      location
    };

    const docId = await CustomersService.createCustomer(newCustomer);

    return NextResponse.json({
      success: true,
      data: { id: docId, ...newCustomer },
      message: 'Customer added successfully'
    });
  } catch (error) {
    console.error('Error creating customer:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to add customer' },
      { status: 500 }
    );
  }
}

// PUT - Update customer
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, email, phone, address, status, preferredPaymentMethod, notes, location } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Customer ID is required' },
        { status: 400 }
      );
    }

    const existingCustomer = await CustomersService.getCustomer(id);
    if (!existingCustomer) {
      return NextResponse.json(
        { success: false, error: 'Customer not found' },
        { status: 404 }
      );
    }

    // Check if email already exists (excluding current customer)
    if (email && email !== existingCustomer.email) {
      const customerWithEmail = await CustomersService.getCustomerByEmail(email);
      if (customerWithEmail) {
        return NextResponse.json(
          { success: false, error: 'Email already exists' },
          { status: 400 }
        );
      }
    }

    // Prepare updates
    const updates: Record<string, unknown> = {};
    if (name) updates.name = name;
    if (email) updates.email = email;
    if (phone) updates.phone = phone;
    if (address) updates.address = address;
    if (status) updates.status = status;
    if (preferredPaymentMethod) updates.preferredPaymentMethod = preferredPaymentMethod;
    if (notes !== undefined) updates.notes = notes;
    if (body.isVip !== undefined) updates.isVIP = body.isVip;
    if (location) updates.location = location;

    await CustomersService.updateCustomer(id, updates);

    const updatedCustomer = await CustomersService.getCustomer(id);

    return NextResponse.json({
      success: true,
      data: updatedCustomer,
      message: 'Customer updated successfully'
    });
  } catch (error) {
    console.error('Error updating customer:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update customer' },
      { status: 500 }
    );
  }
}

// DELETE - Remove customer
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { id } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Customer ID is required' },
        { status: 400 }
      );
    }

    const existingCustomer = await CustomersService.getCustomer(id);
    if (!existingCustomer) {
      return NextResponse.json(
        { success: false, error: 'Customer not found' },
        { status: 404 }
      );
    }

    await CustomersService.deleteCustomer(id);

    return NextResponse.json({
      success: true,
      data: existingCustomer,
      message: 'Customer deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting customer:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete customer' },
      { status: 500 }
    );
  }
}

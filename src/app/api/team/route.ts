import { NextRequest, NextResponse } from 'next/server';

// Team member types
type TeamRole = 'Sales' | 'Purchasing' | 'Delivery' | 'Management' | 'Support';

type TeamMember = {
  id: string;
  name: string;
  role: TeamRole;
  email: string;
  phone: string;
  location: string;
  status: 'Active' | 'Inactive';
  joinDate: string;
  avatar?: string;
};

// Team members data - starts empty
const teamMembers: TeamMember[] = [];

// GET - Fetch all team members
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');
    const status = searchParams.get('status');

    let filteredMembers = teamMembers;

    // Filter by role if specified
    if (role && role !== 'All') {
      filteredMembers = filteredMembers.filter(member => member.role === role);
    }

    // Filter by status if specified
    if (status) {
      filteredMembers = filteredMembers.filter(member => member.status === status);
    }

    return NextResponse.json({
      success: true,
      data: filteredMembers,
      message: 'Team members retrieved successfully'
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to fetch team members' },
      { status: 500 }
    );
  }
}

// POST - Add new team member
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, role, email, phone, location, status, joinDate } = body;

    // Validate required fields
    if (!name || !role || !email || !phone || !location) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingMember = teamMembers.find(member => member.email === email);
    if (existingMember) {
      return NextResponse.json(
        { success: false, error: 'Email already exists' },
        { status: 400 }
      );
    }

    // Create new team member
    const newMember: TeamMember = {
      id: `tm-${Date.now()}`,
      name,
      role,
      email,
      phone,
      location,
      status: status || 'Active',
      joinDate: joinDate || new Date().toISOString().split('T')[0]
    };

    teamMembers.push(newMember);

    return NextResponse.json({
      success: true,
      data: newMember,
      message: 'Team member added successfully'
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to add team member' },
      { status: 500 }
    );
  }
}

// PUT - Update team member
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, role, email, phone, location, status, joinDate } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Team member ID is required' },
        { status: 400 }
      );
    }

    const memberIndex = teamMembers.findIndex(member => member.id === id);

    if (memberIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Team member not found' },
        { status: 404 }
      );
    }

    // Check if email already exists (excluding current member)
    if (email) {
      const existingMember = teamMembers.find(member => member.email === email && member.id !== id);
      if (existingMember) {
        return NextResponse.json(
          { success: false, error: 'Email already exists' },
          { status: 400 }
        );
      }
    }

    // Update team member
    const updatedMember = {
      ...teamMembers[memberIndex],
      ...(name && { name }),
      ...(role && { role }),
      ...(email && { email }),
      ...(phone && { phone }),
      ...(location && { location }),
      ...(status && { status }),
      ...(joinDate && { joinDate })
    };

    teamMembers[memberIndex] = updatedMember;

    return NextResponse.json({
      success: true,
      data: updatedMember,
      message: 'Team member updated successfully'
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to update team member' },
      { status: 500 }
    );
  }
}

// DELETE - Remove team member
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { id } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Team member ID is required' },
        { status: 400 }
      );
    }

    const memberIndex = teamMembers.findIndex(member => member.id === id);

    if (memberIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Team member not found' },
        { status: 404 }
      );
    }

    const deletedMember = teamMembers[memberIndex];
    teamMembers.splice(memberIndex, 1);

    return NextResponse.json({
      success: true,
      data: deletedMember,
      message: 'Team member deleted successfully'
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to delete team member' },
      { status: 500 }
    );
  }
}

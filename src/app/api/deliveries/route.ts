import { NextRequest, NextResponse } from 'next/server';
import { DeliveriesService } from '@/services/firebase/deliveries';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const driver = searchParams.get('driver');
    const priority = searchParams.get('priority');
    const limit = searchParams.get('limit');

    let deliveries;

    // Get deliveries based on filters
    if (status && status !== 'all') {
      deliveries = await DeliveriesService.getDeliveriesByStatus(status);
    } else if (driver) {
      deliveries = await DeliveriesService.getDeliveriesByDriver(driver);
    } else if (limit) {
      const limitNum = parseInt(limit);
      deliveries = await DeliveriesService.getRecentDeliveries(limitNum);
    } else {
      deliveries = await DeliveriesService.getAllDeliveries();
    }

    // Additional filtering for priority (client-side for now)
    let filteredDeliveries = deliveries;
    if (priority && priority !== 'all') {
      filteredDeliveries = deliveries.filter(delivery =>
        delivery.priority.toLowerCase() === priority.toLowerCase()
      );
    }

    // Convert Firestore data to match expected format
    const formattedDeliveries = filteredDeliveries.map(delivery => ({
      id: delivery.id,
      orderId: delivery.orderId,
      address: delivery.address,
      driver: delivery.driver,
      driverPhone: delivery.driverPhone,
      eta: delivery.eta,
      status: delivery.status,
      coordinates: delivery.coordinates,
      estimatedDistance: delivery.estimatedDistance,
      priority: delivery.priority
    }));

    return NextResponse.json({
      success: true,
      data: formattedDeliveries,
      total: formattedDeliveries.length
    });
  } catch (error) {
    console.error('Error fetching deliveries:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch deliveries' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const { orderId, address, driver, driverPhone, eta, priority } = body;

    if (!orderId || !address || !driver || !driverPhone || !eta) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const newDelivery = {
      orderId,
      address,
      driver,
      driverPhone,
      eta,
      status: 'On Time' as 'On Time' | 'Delayed' | 'Early' | 'Delivered' | 'Cancelled',
      coordinates: { lat: 0, lng: 0 }, // Would be calculated from address
      estimatedDistance: 'Calculating...',
      priority: (priority || 'Medium') as 'Low' | 'Medium' | 'High' | 'Urgent'
    };

    const docId = await DeliveriesService.createDelivery(newDelivery);

    return NextResponse.json({
      success: true,
      data: { id: docId, ...newDelivery },
      message: 'Delivery created successfully'
    });
  } catch (error) {
    console.error('Error creating delivery:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create delivery' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, status, eta, coordinates } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Delivery ID is required' },
        { status: 400 }
      );
    }

    const existingDelivery = await DeliveriesService.getDelivery(id);
    if (!existingDelivery) {
      return NextResponse.json(
        { success: false, error: 'Delivery not found' },
        { status: 404 }
      );
    }

    // Prepare updates
    const updates: Record<string, unknown> = {};
    if (status) updates.status = status;
    if (eta) updates.eta = eta;
    if (coordinates) updates.coordinates = coordinates;

    await DeliveriesService.updateDelivery(id, updates);

    // Get updated delivery
    const updatedDelivery = await DeliveriesService.getDelivery(id);

    return NextResponse.json({
      success: true,
      data: updatedDelivery,
      message: 'Delivery updated successfully'
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to update delivery' },
      { status: 500 }
    );
  }
}

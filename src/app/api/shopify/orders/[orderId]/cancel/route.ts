import { NextRequest, NextResponse } from 'next/server';

export async function PUT(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const body = await request.json();
    const { reason, notes } = body;

    // Log the cancellation request
    console.log(`Cancelling order ${orderId} with reason: ${reason}`, notes ? `Notes: ${notes}` : '');

    // In a real implementation, this would:
    // 1. Validate the order exists
    // 2. Check if the order can be cancelled
    // 3. Call Shopify API to cancel the order
    // 4. Update the order status in the database
    // 5. Send cancellation email to customer
    // 6. Handle refunds if necessary

    // For now, we'll simulate a successful cancellation
    // You can uncomment the following line to simulate API failures for testing:
    // throw new Error('Simulated API failure');

    // Simulate API processing time
    await new Promise(resolve => setTimeout(resolve, 500));

    // Return success response
    return NextResponse.json({
      success: true,
      message: `Order ${orderId} cancelled successfully`,
      data: {
        orderId,
        status: 'cancelled',
        reason,
        notes,
        cancelledAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error cancelling order:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to cancel order',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

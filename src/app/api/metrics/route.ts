import { NextRequest, NextResponse } from 'next/server';
import { MetricsService } from '@/services/firebase/metrics';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const period = searchParams.get('period') || '7d';

    // Get real metrics from Firestore with period filtering
    const metrics = await MetricsService.getDashboardMetrics(period);

    // Return specific metric type if requested
    if (type) {
      switch (type) {
        case 'daily':
          return NextResponse.json({
            success: true,
            data: {
              orders: metrics.orderTrends.map(trend => ({ date: trend.date, value: trend.orders })),
              revenue: metrics.orderTrends.map(trend => ({ date: trend.date, value: trend.revenue })),
              deliveries: metrics.orderTrends.map(trend => ({
                date: trend.date,
                completed: Math.floor(trend.orders * 0.9),
                failed: Math.floor(trend.orders * 0.1)
              }))
            },
            period
          });
        case 'summary':
          return NextResponse.json({
            success: true,
            data: {
              totalOrders: metrics.totalOrders,
              activeDeliveries: metrics.recentDeliveries.length,
              pendingOrders: metrics.pendingOrders,
              revenue: metrics.totalRevenue,
              successRate: metrics.totalOrders > 0 ? (metrics.completedOrders / metrics.totalOrders * 100) : 0,
              avgDeliveryTime: metrics.averageDeliveryTime
            }
          });
        default:
          return NextResponse.json({
            success: true,
            data: metrics
          });
      }
    }

    // Return formatted metrics for dashboard
    const formattedMetrics = {
      totalOrders: {
        current: metrics.totalOrders,
        previous: 0, // Would calculate from previous period
        trend: 0,
        trendLabel: 'vs last month'
      },
      activeDeliveries: {
        current: metrics.recentDeliveries.length,
        previous: 0,
        trend: 0,
        trendLabel: 'vs yesterday'
      },
      pendingOrders: {
        current: metrics.pendingOrders,
        previous: 0,
        trend: 0,
        trendLabel: 'vs last week'
      },
      revenue: {
        current: metrics.totalRevenue,
        previous: 0,
        trend: 0,
        trendLabel: 'vs last month'
      },
      deliverySuccess: {
        current: metrics.totalOrders > 0 ? Math.round(metrics.completedOrders / metrics.totalOrders * 100) : 0,
        previous: 0,
        trend: 0,
        trendLabel: 'vs last month'
      },
      averageDeliveryTime: {
        current: metrics.averageDeliveryTime,
        previous: 0,
        trend: 0,
        trendLabel: 'hours vs last month'
      },
      customerSatisfaction: {
        current: metrics.customerSatisfaction,
        previous: 0,
        trend: 0,
        trendLabel: 'vs last month'
      },
      activeDrivers: {
        current: metrics.activeTrucks,
        previous: 0,
        trend: 0,
        trendLabel: 'vs last week'
      }
    };

    return NextResponse.json({
      success: true,
      data: formattedMetrics,
      period,
      lastUpdated: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching metrics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch metrics' },
      { status: 500 }
    );
  }
}

// POST endpoint for updating metrics (in a real app, this would be automated)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { metric, value, period } = body;

    if (!metric || value === undefined) {
      return NextResponse.json(
        { success: false, error: 'Metric name and value are required' },
        { status: 400 }
      );
    }

    // In a real app, this would update the database
    // For now, we'll just simulate the update

    return NextResponse.json({
      success: true,
      message: `Metric ${metric} updated successfully`,
      data: {
        metric,
        value,
        period: period || '7d',
        updatedAt: new Date().toISOString()
      }
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to update metric' },
      { status: 500 }
    );
  }
}

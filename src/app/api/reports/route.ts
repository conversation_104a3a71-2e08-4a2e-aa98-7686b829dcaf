import { NextRequest, NextResponse } from 'next/server';
import { OrdersService } from '@/services/firebase/orders';
import { CustomersService } from '@/services/firebase/customers';
import { TeamService } from '@/services/firebase/team';
import { FleetService } from '@/services/firebase/fleet';

// Custom Report Builder Types
type DataSource = 'orders' | 'customers' | 'deliveries' | 'team' | 'fleet';
type ChartType = 'bar' | 'line' | 'pie' | 'table' | 'metric';
type TimePeriod = '7d' | '30d' | '3m' | '6m' | '1y' | 'all';

type ReportConfig = {
  id: string;
  name: string;
  dataSource: DataSource;
  chartType: ChartType;
  period: TimePeriod;
  filters: Record<string, unknown>;
  groupBy?: string;
  aggregation?: 'count' | 'sum' | 'avg' | 'min' | 'max';
  field?: string;
  createdAt: string;
  lastRun?: string;
};

type GeneratedReport = {
  id: string;
  config: ReportConfig;
  data: Array<Record<string, unknown>>;
  summary: {
    totalRecords: number;
    totalValue?: number;
    averageValue?: number;
  };
  generatedAt: string;
};

// Generate report data based on configuration
const generateReportData = async (config: ReportConfig): Promise<GeneratedReport> => {
  try {
    let data: Array<Record<string, unknown>> = [];
    let summary: {
      totalRecords: number;
      totalValue?: number;
      averageValue?: number;
    } = {
      totalRecords: 0
    };

    // Get data based on data source
    switch (config.dataSource) {
      case 'orders':
        const orders = await OrdersService.getAllOrders();
        data = orders.map(order => ({
          orderId: order.orderId,
          customerName: order.customerName,
          orderType: order.orderType,
          status: order.status,
          totalAmount: order.totalAmount,
          createdAt: order.createdAt.toDate().toISOString(),
          deliveryAgent: order.deliveryAgent || 'N/A'
        }));

        const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
        summary = {
          totalRecords: orders.length,
          totalValue: totalRevenue,
          averageValue: orders.length > 0 ? Math.round(totalRevenue / orders.length) : 0
        };
        break;

      case 'customers':
        const customers = await CustomersService.getAllCustomers();
        data = customers.map(customer => ({
          name: customer.name,
          email: customer.email,
          phone: customer.phone,
          status: customer.status,
          isVIP: customer.isVIP,
          totalOrders: customer.totalOrders,
          totalSpent: customer.totalSpent,
          joinDate: customer.joinDate.toDate().toISOString()
        }));

        const totalSpent = customers.reduce((sum, customer) => sum + customer.totalSpent, 0);
        summary = {
          totalRecords: customers.length,
          totalValue: totalSpent,
          averageValue: customers.length > 0 ? Math.round(totalSpent / customers.length) : 0
        };
        break;

      case 'team':
        const teamMembers = await TeamService.getAllTeamMembers();
        data = teamMembers.map(member => ({
          name: member.name,
          role: member.role,
          status: member.status,
          phone: member.phone,
          email: member.email,
          joinDate: member.joinDate.toDate().toISOString(),
          ordersHandled: 0 // This would be calculated from actual data
        }));

        summary = {
          totalRecords: teamMembers.length
        };
        break;

      case 'fleet':
        const fleetData = await FleetService.getAllTrucks();
        data = fleetData.map(truck => ({
          plateNumber: truck.licensePlate,
          model: truck.model,
          status: truck.status,
          driver: truck.driverName || 'N/A',
          capacity: truck.capacity,
          deliveriesCompleted: 0 // This would be calculated from actual data
        }));

        summary = {
          totalRecords: fleetData.length
        };
        break;

      default:
        // Mock data for deliveries or other sources
        data = Array.from({ length: 10 }, (_, i) => ({
          id: `item-${i + 1}`,
          name: `Item ${i + 1}`,
          value: Math.floor(Math.random() * 1000) + 100
        }));

        summary = {
          totalRecords: data.length
        };
    }

    // Apply time period filtering
    if (config.period !== 'all') {
      const now = new Date();
      const periodDays = {
        '7d': 7,
        '30d': 30,
        '3m': 90,
        '6m': 180,
        '1y': 365
      }[config.period] || 30;

      const cutoffDate = new Date(now.getTime() - periodDays * 24 * 60 * 60 * 1000);

      data = data.filter(item => {
        const itemDate = new Date(item.createdAt as string || item.joinDate as string || now);
        return itemDate >= cutoffDate;
      });

      // Recalculate summary for filtered data
      if (config.dataSource === 'orders' || config.dataSource === 'customers') {
        const totalValue = data.reduce((sum, item) => sum + (item.totalAmount as number || item.totalSpent as number || 0), 0);
        summary = {
          totalRecords: data.length,
          totalValue,
          averageValue: data.length > 0 ? Math.round(totalValue / data.length) : 0
        };
      } else {
        summary.totalRecords = data.length;
      }
    }

    return {
      id: `gen-${Date.now()}`,
      config: {
        ...config,
        lastRun: new Date().toISOString()
      },
      data,
      summary,
      generatedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error generating report data:', error);
    throw error;
  }
};

// GET - Generate and return report data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const configParam = searchParams.get('config');

    if (!configParam) {
      return NextResponse.json(
        { success: false, error: 'Report configuration is required' },
        { status: 400 }
      );
    }

    let config: ReportConfig;
    try {
      config = JSON.parse(configParam);
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid report configuration' },
        { status: 400 }
      );
    }

    const report = await generateReportData(config);

    return NextResponse.json({
      success: true,
      data: report
    });
  } catch (error) {
    console.error('Error generating report:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate report' },
      { status: 500 }
    );
  }
}

// POST - Generate report from configuration
export async function POST(request: NextRequest) {
  try {
    const config: ReportConfig = await request.json();

    if (!config.name || !config.dataSource || !config.chartType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const report = await generateReportData(config);

    return NextResponse.json({
      success: true,
      data: report,
      message: 'Report generated successfully'
    });
  } catch (error) {
    console.error('Error generating report:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate report' },
      { status: 500 }
    );
  }
}

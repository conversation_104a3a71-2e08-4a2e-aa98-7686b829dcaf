'use client';

import React, { useState, useEffect } from 'react';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  transitionKey?: string;
  animationType?: 'fade' | 'slide';
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className = '',
  transitionKey,
  animationType = 'fade'
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Trigger enter animation immediately to prevent flashing
    setIsVisible(true);
  }, [transitionKey]);

  const getAnimationClass = () => {
    if (isVisible) {
      return animationType === 'slide' ? 'page-slide-enter' : 'page-enter';
    }
    return 'opacity-0';
  };

  return (
    <div className={`page-transition-wrapper ${getAnimationClass()} ${className}`}>
      {children}
    </div>
  );
};

// Hook for managing page transitions
export const usePageTransition = () => {
  const [isTransitioning, setIsTransitioning] = useState(false);

  const startTransition = async (navigationFn: () => void, delay: number = 300) => {
    setIsTransitioning(true);

    // Add transition class to body to prevent interactions
    document.body.classList.add('nav-transitioning');

    // Wait for exit animation
    await new Promise(resolve => setTimeout(resolve, delay));

    // Execute navigation
    navigationFn();

    // Clean up after navigation
    setTimeout(() => {
      setIsTransitioning(false);
      document.body.classList.remove('nav-transitioning');
    }, 100);
  };

  return {
    isTransitioning,
    startTransition
  };
};

export default PageTransition;

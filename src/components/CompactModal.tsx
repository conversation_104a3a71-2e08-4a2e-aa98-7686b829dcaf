import React, { ReactNode } from 'react';
import { useLocale } from 'next-intl';

interface CompactModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  maxWidth?: string; // e.g. 'max-w-xl', 'max-w-2xl', 'max-w-3xl'
}

/**
 * A reusable compact modal component with a consistent design
 */
export const CompactModal: React.FC<CompactModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  maxWidth = 'max-w-xl'
}) => {
  const locale = useLocale();
  const isRTL = locale === 'ar';


  if (!isOpen) return null;

  // Handle click on the backdrop to close the modal
  const handleBackdropClick = (e: React.MouseEvent) => {
    // Only close if clicking the backdrop, not the modal content
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-2 sm:p-4 backdrop-blur-sm animate-fadeIn"
      onClick={handleBackdropClick}
    >
      <div className={`bg-white rounded-2xl p-0 ${maxWidth} w-full max-h-[90vh] overflow-hidden shadow-2xl animate-scaleIn relative`}>
        {/* Compact header with gradient background */}
        <div className={`bg-gradient-to-r ${isRTL ? 'from-purple-600 to-indigo-600' : 'from-indigo-600 to-purple-600'} py-3 px-5 text-white rounded-t-2xl flex items-center justify-between relative`}>
          <h2 className={`text-lg font-bold`}>{title}</h2>
        </div>

        {/* Modal content with scroll */}
        <div className="p-3 sm:p-5 overflow-y-auto max-h-[calc(90vh-60px)]">
          {children}
        </div>
      </div>
    </div>
  );
};

/**
 * FormField component for consistent form fields in modals
 */
export const FormField: React.FC<{
  label: string;
  children: ReactNode;
  error?: string;
  required?: boolean;
  className?: string;
}> = ({ label, children, error, required, className = '' }) => {
  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-1 sm:mb-2">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <div className="relative">
        {children}
      </div>
      {error && (
        <p className="text-red-500 text-xs mt-1 sm:mt-2">{error}</p>
      )}
    </div>
  );
};

/**
 * Modal form buttons for a consistent footer
 */
export const ModalActions: React.FC<{
  onCancel: () => void;
  cancelText: string;
  submitText: string;
  icon?: ReactNode;
  cancelIcon?: ReactNode;
  showCancel?: boolean;
}> = ({ onCancel, cancelText, submitText, icon, cancelIcon, showCancel = true }) => {
  const locale = useLocale();
  const isRTL = locale === 'ar';

  return (
    <div className={`flex flex-col sm:flex-row gap-3 pt-3 mt-2 border-t border-gray-100 ${!showCancel ? 'sm:justify-center' : ''}`}>
      {showCancel && (
        <button
          type="button"
          onClick={onCancel}
          className={`w-full sm:flex-1 py-2.5 sm:py-2 px-4 border border-gray-300 text-gray-700 rounded-lg font-medium text-sm hover:bg-gray-50 transition duration-200 flex items-center justify-center gap-2 ${isRTL ? 'sm:order-2' : 'sm:order-1'} order-2 sm:order-none`}
        >
          {cancelIcon}
          <span className="font-medium">{cancelText}</span>
        </button>
      )}
      <button
        type="submit"
        className={`w-full ${showCancel ? 'sm:flex-1' : 'sm:px-8'} bg-gradient-to-r ${isRTL ? 'from-purple-600 to-indigo-600' : 'from-indigo-600 to-purple-600'} text-white py-2.5 sm:py-2 px-4 rounded-lg font-medium text-sm hover:from-indigo-700 hover:to-purple-700 transition duration-200 shadow-md hover:shadow-lg flex items-center justify-center gap-2 ${isRTL ? 'sm:order-1' : 'sm:order-2'} order-1 sm:order-none`}
      >
        {icon}
        <span className="font-medium">{submitText}</span>
      </button>
    </div>
  );
};

/**
 * Responsive two-column grid layout for form fields
 */
export const TwoColumnForm: React.FC<{
  children: ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 gap-x-3 sm:gap-x-4 gap-y-3 sm:gap-y-4 ${className}`}>
      {children}
    </div>
  );
};

/**
 * Full-width form field (spans both columns)
 */
export const FullWidthField: React.FC<{
  children: ReactNode;
}> = ({ children }) => {
  return (
    <div className="col-span-2">
      {children}
    </div>
  );
};

/**
 * Input field with consistent styling and icon
 */
export const IconInput: React.FC<{
  icon: ReactNode;
  className?: string;
  [key: string]: unknown; // For other props like value, onChange, etc.
}> = ({ icon, className = '', ...props }) => {
  const locale = useLocale();
  const isRTL = locale === 'ar';

  return (
    <div className="relative">
      <input
        className={`w-full px-3 sm:px-4 py-2.5 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${isRTL ? 'pr-8 sm:pr-9' : 'pl-8 sm:pl-9'} text-sm ${className}`}
        dir={isRTL ? 'rtl' : 'ltr'}
        {...props}
      />
      <span className={`absolute ${isRTL ? 'right-2.5 sm:right-3' : 'left-2.5 sm:left-3'} top-1/2 -translate-y-1/2 text-gray-400`}>
        {icon}
      </span>
    </div>
  );
};

/**
 * Select field with consistent styling and icon
 */
export const IconSelect: React.FC<{
  icon: ReactNode;
  children: ReactNode;
  className?: string;
  [key: string]: unknown; // For other props
}> = ({ icon, children, className = '', ...props }) => {
  const locale = useLocale();
  const isRTL = locale === 'ar';

  return (
    <div className="relative">
      <select
        className={`w-full px-3 sm:px-4 py-2.5 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${isRTL ? 'pr-8 sm:pr-9' : 'pl-8 sm:pl-9'} appearance-none text-sm ${className}`}
        dir={isRTL ? 'rtl' : 'ltr'}
        {...props}
      >
        {children}
      </select>
      <span className={`absolute ${isRTL ? 'right-2.5 sm:right-3' : 'left-2.5 sm:left-3'} top-1/2 -translate-y-1/2 text-gray-400`}>
        {icon}
      </span>
      <span className={`absolute ${isRTL ? 'left-2.5 sm:left-3' : 'right-2.5 sm:right-3'} top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none`}>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </span>
    </div>
  );
};
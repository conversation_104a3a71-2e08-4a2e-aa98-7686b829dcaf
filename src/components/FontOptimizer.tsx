'use client';

import { useEffect } from 'react';

export default function FontOptimizer() {
  useEffect(() => {
    // Only preload fonts that are actually used
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Font is needed, ensure it's loaded
          const element = entry.target as HTMLElement;

          // Mark font as used
          element.setAttribute('data-font-loaded', 'true');
        }
      });
    });

    // Observe elements that use custom fonts
    const elementsWithFonts = document.querySelectorAll('[class*="font-"]');
    elementsWithFonts.forEach((el) => observer.observe(el));

    return () => {
      observer.disconnect();
    };
  }, []);

  return null; // This component doesn't render anything
}

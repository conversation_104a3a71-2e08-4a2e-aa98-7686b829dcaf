'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  BarChart3,
  Settings,
  Send,
  Info,
  CheckCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { systemAlertService } from '@/services/notifications/systemAlerts';
import { weeklyReportService } from '@/services/notifications/weeklyReports';

const AdminNotificationPanel: React.FC = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSendMaintenanceAlert = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      const maintenanceTime = new Date();
      maintenanceTime.setHours(maintenanceTime.getHours() + 2); // 2 hours from now
      
      await systemAlertService.sendMaintenanceAlert(
        'Scheduled System Maintenance',
        'System maintenance will be performed to improve performance and add new features',
        maintenanceTime,
        '1 hour',
        user.uid
      );
      
      setMessage('Maintenance alert sent successfully!');
    } catch (error) {
      setMessage('Failed to send maintenance alert');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendUpdateAlert = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      await systemAlertService.sendUpdateAlert(
        'v2.1.0',
        ['Enhanced notification system', 'Improved order tracking', 'Better performance'],
        user.uid
      );
      
      setMessage('Update alert sent successfully!');
    } catch (error) {
      setMessage('Failed to send update alert');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendWeeklyReport = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      await weeklyReportService.sendWeeklyReportNotification(user.uid);
      setMessage('Weekly report sent successfully!');
    } catch (error) {
      setMessage('Failed to send weekly report');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendInfoAlert = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      await systemAlertService.sendInfoAlert(
        'New Feature Available',
        'The enhanced notification system is now live! You can customize your notification preferences in Settings.',
        [],
        user.uid
      );
      
      setMessage('Info alert sent successfully!');
    } catch (error) {
      setMessage('Failed to send info alert');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear message after 3 seconds
  React.useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(''), 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center gap-3 mb-6">
        <Settings className="w-6 h-6 text-indigo-600" />
        <h3 className="text-lg font-semibold text-gray-900">Admin Notification Panel</h3>
      </div>

      {message && (
        <div className={`mb-4 p-3 rounded-lg flex items-center gap-2 ${
          message.includes('successfully') 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.includes('successfully') ? (
            <CheckCircle className="w-4 h-4" />
          ) : (
            <AlertTriangle className="w-4 h-4" />
          )}
          {message}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Maintenance Alert */}
        <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Maintenance Alert</h4>
              <p className="text-sm text-gray-600">Send scheduled maintenance notification</p>
            </div>
          </div>
          <button
            onClick={handleSendMaintenanceAlert}
            disabled={isLoading}
            className="w-full px-4 py-2 bg-yellow-600 text-white text-sm rounded-lg hover:bg-yellow-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            <Send className="w-4 h-4" />
            Send Maintenance Alert
          </button>
        </div>

        {/* System Update */}
        <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Info className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">System Update</h4>
              <p className="text-sm text-gray-600">Notify about new system features</p>
            </div>
          </div>
          <button
            onClick={handleSendUpdateAlert}
            disabled={isLoading}
            className="w-full px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            <Send className="w-4 h-4" />
            Send Update Alert
          </button>
        </div>

        {/* Weekly Report */}
        <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <BarChart3 className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Weekly Report</h4>
              <p className="text-sm text-gray-600">Generate and send performance report</p>
            </div>
          </div>
          <button
            onClick={handleSendWeeklyReport}
            disabled={isLoading}
            className="w-full px-4 py-2 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            <BarChart3 className="w-4 h-4" />
            Send Weekly Report
          </button>
        </div>

        {/* Info Alert */}
        <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Info className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Info Alert</h4>
              <p className="text-sm text-gray-600">Send informational notification</p>
            </div>
          </div>
          <button
            onClick={handleSendInfoAlert}
            disabled={isLoading}
            className="w-full px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            <Info className="w-4 h-4" />
            Send Info Alert
          </button>
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Admin Panel Features:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Send system maintenance notifications</li>
          <li>• Announce system updates and new features</li>
          <li>• Generate and distribute weekly performance reports</li>
          <li>• Send informational alerts to users</li>
          <li>• All notifications respect user preferences</li>
        </ul>
      </div>
    </div>
  );
};

export default AdminNotificationPanel;

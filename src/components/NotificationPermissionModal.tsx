'use client';

import React, { useState } from 'react';
import {
  Bell,
  X,
  CheckCircle,
  AlertTriangle,
  Package,
  BarChart3,
  Shield,
  Smartphone
} from 'lucide-react';
import NotificationPreview from './NotificationPreview';

interface NotificationPermissionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
  onDecline: () => void;
}

const NotificationPermissionModal: React.FC<NotificationPermissionModalProps> = ({
  isOpen,
  onClose,
  onAccept,
  onDecline
}) => {
  const [isClosing, setIsClosing] = useState(false);
  const [showDemo, setShowDemo] = useState(false);
  const [previewType, setPreviewType] = useState<'order' | 'system' | 'report'>('order');

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      setIsClosing(false);
    }, 200);
  };

  const handleAccept = () => {
    onAccept();
    handleClose();
  };

  const handleDecline = () => {
    onDecline();
    handleClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div 
        className={`relative bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-200 ${
          isClosing ? 'scale-95 opacity-0' : 'scale-100 opacity-100'
        }`}
        style={{
          animation: isClosing ? 'slideOut 0.2s ease-in' : 'slideIn 0.3s ease-out'
        }}
      >
        {/* Header */}
        <div className="relative p-6 pb-4 bg-gradient-to-r from-indigo-50 to-blue-50">
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>

          <div className="flex items-center gap-4 mb-4">
            <div className="relative">
              <div className="p-3 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl shadow-lg">
                <Bell className="w-8 h-8 text-white" />
              </div>
              {/* Animated notification dot */}
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              </div>
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Enable Notifications</h2>
              <p className="text-sm text-gray-600">Stay updated with Zawaya Delivery</p>
              <div className="flex items-center gap-1 mt-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs text-green-600 font-medium">Secure & Private</span>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 pb-6">
          <p className="text-gray-700 mb-6 leading-relaxed">
            Get real-time updates about your orders and important system alerts. 
            We&apos;ll only send you relevant notifications to keep you informed.
          </p>

          {/* Features */}
          <div className="space-y-3 mb-6">
            <button
              onClick={() => {
                setPreviewType('order');
                setShowDemo(true);
                setTimeout(() => setShowDemo(false), 4000);
              }}
              className="w-full flex items-center gap-3 p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors cursor-pointer"
            >
              <div className="p-1.5 bg-green-100 rounded-lg">
                <Package className="w-4 h-4 text-green-600" />
              </div>
              <div className="text-left">
                <p className="font-medium text-green-900 text-sm">Order Updates</p>
                <p className="text-green-700 text-xs">Status changes, deliveries, and cancellations</p>
              </div>
              <div className="ml-auto text-green-600 text-xs">Preview →</div>
            </button>

            <button
              onClick={() => {
                setPreviewType('system');
                setShowDemo(true);
                setTimeout(() => setShowDemo(false), 4000);
              }}
              className="w-full flex items-center gap-3 p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors cursor-pointer"
            >
              <div className="p-1.5 bg-blue-100 rounded-lg">
                <AlertTriangle className="w-4 h-4 text-blue-600" />
              </div>
              <div className="text-left">
                <p className="font-medium text-blue-900 text-sm">System Alerts</p>
                <p className="text-blue-700 text-xs">Maintenance notifications and important updates</p>
              </div>
              <div className="ml-auto text-blue-600 text-xs">Preview →</div>
            </button>

            <button
              onClick={() => {
                setPreviewType('report');
                setShowDemo(true);
                setTimeout(() => setShowDemo(false), 4000);
              }}
              className="w-full flex items-center gap-3 p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors cursor-pointer"
            >
              <div className="p-1.5 bg-purple-100 rounded-lg">
                <BarChart3 className="w-4 h-4 text-purple-600" />
              </div>
              <div className="text-left">
                <p className="font-medium text-purple-900 text-sm">Performance Reports</p>
                <p className="text-purple-700 text-xs">Weekly summaries and analytics</p>
              </div>
              <div className="ml-auto text-purple-600 text-xs">Preview →</div>
            </button>
          </div>

          {/* Privacy Note */}
          <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg mb-6">
            <Shield className="w-4 h-4 text-gray-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-xs text-gray-600 leading-relaxed">
                <span className="font-medium">Privacy Protected:</span> You can customize notification 
                preferences anytime in Settings. We respect your privacy and won&apos;t spam you.
              </p>
            </div>
          </div>

          {/* Device Info */}
          <div className="flex items-center gap-2 mb-4 text-xs text-gray-500">
            <Smartphone className="w-3 h-3" />
            <span>Works on desktop and mobile browsers</span>
          </div>

          {/* Demo Section */}
          <div className="mb-6">
            <button
              onClick={() => {
                setShowDemo(true);
                // Show a demo notification if permission is already granted
                if (Notification.permission === 'granted') {
                  new Notification('Zawaya Delivery Demo', {
                    body: 'This is how notifications will look! 🚚',
                    icon: '/favicon.ico',
                    tag: 'demo'
                  });
                }
                setTimeout(() => setShowDemo(false), 3000);
              }}
              className="w-full px-4 py-2 text-sm text-indigo-600 bg-indigo-50 hover:bg-indigo-100 rounded-lg font-medium transition-colors border border-indigo-200 relative overflow-hidden"
            >
              <span className="relative z-10">
                👀 {showDemo ? 'Demo Sent!' : 'See Demo Notification'}
              </span>
              {showDemo && (
                <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-blue-500 opacity-20 animate-pulse"></div>
              )}
            </button>
            {Notification.permission !== 'granted' && (
              <p className="text-xs text-gray-500 mt-2 text-center">
                Demo will work after you enable notifications
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={handleDecline}
              className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-medium transition-all duration-200 hover:scale-105 active:scale-95"
            >
              Not Now
            </button>
            <button
              onClick={handleAccept}
              className="flex-1 px-4 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95 flex items-center justify-center gap-2 relative overflow-hidden group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
              <CheckCircle className="w-4 h-4 relative z-10" />
              <span className="relative z-10">Enable Notifications</span>
            </button>
          </div>

          {/* Trust indicators */}
          <div className="mt-4 flex items-center justify-center gap-4 text-xs text-gray-400">
            <div className="flex items-center gap-1">
              <Shield className="w-3 h-3" />
              <span>Privacy Protected</span>
            </div>
            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
            <div className="flex items-center gap-1">
              <CheckCircle className="w-3 h-3" />
              <span>Easy to Disable</span>
            </div>
          </div>
        </div>
      </div>

      {/* Notification Preview */}
      <NotificationPreview
        type={previewType}
        isVisible={showDemo}
      />

      <style jsx>{`
        @keyframes slideIn {
          0% {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
          }
          100% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
        
        @keyframes slideOut {
          0% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
          100% {
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
          }
        }
      `}</style>
    </div>
  );
};

export default NotificationPermissionModal;

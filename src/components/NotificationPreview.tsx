'use client';

import React from 'react';
import { Package, AlertTriangle, BarChart3 } from 'lucide-react';

interface NotificationPreviewProps {
  type: 'order' | 'system' | 'report';
  isVisible: boolean;
}

const NotificationPreview: React.FC<NotificationPreviewProps> = ({ type, isVisible }) => {
  const getNotificationContent = () => {
    switch (type) {
      case 'order':
        return {
          icon: Package,
          title: 'Order ORD-001 Delivered',
          body: 'Your order has been successfully delivered to Ahmed Hassan',
          color: 'from-green-500 to-emerald-600'
        };
      case 'system':
        return {
          icon: AlertTriangle,
          title: 'System Maintenance',
          body: 'Scheduled maintenance will begin in 30 minutes',
          color: 'from-yellow-500 to-orange-600'
        };
      case 'report':
        return {
          icon: BarChart3,
          title: 'Weekly Report Available',
          body: '25 orders, 92% delivery rate, 15,750 EGP revenue',
          color: 'from-purple-500 to-indigo-600'
        };
      default:
        return {
          icon: Package,
          title: 'Notification',
          body: 'This is a sample notification',
          color: 'from-blue-500 to-indigo-600'
        };
    }
  };

  const { icon: Icon, title, body, color } = getNotificationContent();

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 right-4 z-50 animate-slideInRight">
      <div className="bg-white rounded-lg shadow-2xl border border-gray-200 max-w-sm w-full overflow-hidden">
        {/* Header */}
        <div className={`h-1 bg-gradient-to-r ${color}`}></div>
        
        {/* Content */}
        <div className="p-4">
          <div className="flex items-start gap-3">
            {/* Icon */}
            <div className={`p-2 rounded-lg bg-gradient-to-br ${color} flex-shrink-0`}>
              <Icon className="w-4 h-4 text-white" />
            </div>
            
            {/* Text Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-semibold text-gray-900 text-sm truncate">{title}</h4>
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
              </div>
              <p className="text-gray-600 text-xs leading-relaxed">{body}</p>
              <div className="flex items-center gap-2 mt-2">
                <span className="text-xs text-gray-400">Zawaya Delivery</span>
                <span className="text-xs text-gray-400">•</span>
                <span className="text-xs text-gray-400">now</span>
              </div>
            </div>
            
            {/* Close button */}
            <button className="text-gray-400 hover:text-gray-600 p-1 flex-shrink-0">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
          
          {/* Action buttons for order notifications */}
          {type === 'order' && (
            <div className="flex gap-2 mt-3 pt-3 border-t border-gray-100">
              <button className="flex-1 px-3 py-1.5 text-xs font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                View Details
              </button>
              <button className={`flex-1 px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r ${color} hover:opacity-90 rounded transition-opacity`}>
                Track Order
              </button>
            </div>
          )}
        </div>
      </div>

      <style jsx>{`
        @keyframes slideInRight {
          0% {
            opacity: 0;
            transform: translateX(100%);
          }
          100% {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        .animate-slideInRight {
          animation: slideInRight 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default NotificationPreview;

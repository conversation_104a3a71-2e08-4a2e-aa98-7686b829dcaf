'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigationTranslations, useAuthTranslations } from '@/hooks/useTranslations';
import {
  Package,
  Truck,
  Users,
  TrendingUp,
  LayoutDashboard,
  Settings,
  LogOut,
  Menu,
  X,
  Globe,
  ShoppingCart,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

// Section Header Component
const SectionHeader: React.FC<{
  title: string;
  isCollapsed: boolean;
  onToggle: () => void;
}> = ({ title, isCollapsed, onToggle }) => (
  <div
    className="px-4 py-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors"
    onClick={onToggle}
  >
    <div className="flex items-center justify-between">
      <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider relative">
        {title}
        <div className="absolute -bottom-1 left-0 w-8 h-0.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
      </h3>
      {isCollapsed ? (
        <ChevronDown className="w-3 h-3 text-gray-400" />
      ) : (
        <ChevronUp className="w-3 h-3 text-gray-400" />
      )}
    </div>
  </div>
);

// Navigation Item Component
const NavItem: React.FC<{
  icon: React.ElementType;
  label: string;
  active?: boolean;
  onClick: () => void;
}> = ({ icon: Icon, label, active = false, onClick }) => (
  <li className="mb-1">
    <button
      onClick={onClick}
      className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
        active
          ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg transform scale-[1.02]'
          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900 hover:transform hover:scale-[1.01]'
      }`}
    >
      <Icon className={`w-5 h-5 ${active ? 'text-white' : 'text-gray-500'}`} />
      <span className="font-medium">{label}</span>
    </button>
  </li>
);

// Shared Sidebar Component
export const Sidebar: React.FC<{
  activeItem: string;
  setActiveItem: (item: string) => void;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onNavigate: (path: string) => void;
  onOpenSettings?: () => void;
}> = ({ activeItem, setActiveItem, isOpen, setIsOpen, onNavigate, onOpenSettings }) => {
  const { logout } = useAuth();
  const router = useRouter();
  const t = useNavigationTranslations();
  const auth = useAuthTranslations();

  // State for collapsible sections (all folded by default)
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({
    [t('analyticsOverview')]: true,
    [t('onlineOperations')]: true,
    [t('purchasingProcurement')]: true,
    [t('coreOperations')]: true,
    [t('account')]: true
  });

  // Function to find which section contains the active item
  const findSectionForActiveItem = (activeItem: string) => {
    for (const group of navGroups) {
      if (group.items.some(item => item.id === activeItem)) {
        return group.title;
      }
    }
    // Check if it's in the Account section
    if (activeItem === 'settings') {
      return 'Account';
    }
    return null;
  };

  // Effect to automatically unfold the section containing the active item
  useEffect(() => {
    const activeSection = findSectionForActiveItem(activeItem);
    if (activeSection) {
      setCollapsedSections(prev => ({
        ...prev,
        [activeSection]: false
      }));
    }
  }, [activeItem, findSectionForActiveItem]);

  const handleLogout = async () => {
    try {
      await logout();
      // Extract locale from current path and preserve it
      const currentLocale = window.location.pathname.split('/')[1] || 'en';
      // Ensure we maintain the current locale when redirecting to login
      router.push(`/${currentLocale}/login`);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Reorganized navigation items into 4 logical sections
  const navGroups = [
    {
      title: t('analyticsOverview'),
      items: [
        { id: 'dashboard', label: t('dashboard'), icon: LayoutDashboard, path: '/dashboard' },
        { id: 'reports', label: t('reports'), icon: TrendingUp, path: '/reports' },
      ]
    },
    {
      title: t('onlineOperations'),
      items: [
        { id: 'onlineOrders', label: t('onlineOrders'), icon: Globe, path: '/online-orders' },
      ]
    },
    {
      title: t('purchasingProcurement'),
      items: [
        { id: 'purchasing', label: t('purchasing'), icon: ShoppingCart, path: '/purchasing' },
      ]
    },
    {
      title: t('coreOperations'),
      items: [
        { id: 'orders', label: t('orders'), icon: Package, path: '/orders' },
        { id: 'customers', label: t('customers'), icon: Users, path: '/customers' },
        { id: 'team', label: t('team'), icon: Users, path: '/team' },
        { id: 'fleet', label: t('fleet'), icon: Truck, path: '/fleet' },
      ]
    }
  ];

  // Toggle section collapse state
  const toggleSection = (sectionTitle: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [sectionTitle]: !prev[sectionTitle]
    }));
  };

  // Auto-expand section based on active item
  useEffect(() => {
    const expandSectionForActiveItem = () => {
      let sectionToExpand = '';

      // Map active items to their sections
      if (['dashboard', 'reports'].includes(activeItem)) {
        sectionToExpand = t('analyticsOverview');
      } else if (['onlineOrders'].includes(activeItem)) {
        sectionToExpand = t('onlineOperations');
      } else if (['purchasing'].includes(activeItem)) {
        sectionToExpand = t('purchasingProcurement');
      } else if (['orders', 'customers', 'team', 'fleet'].includes(activeItem)) {
        sectionToExpand = t('coreOperations');
      } else if (['settings'].includes(activeItem)) {
        sectionToExpand = t('account');
      }

      // Expand the relevant section if it's currently collapsed
      if (sectionToExpand && collapsedSections[sectionToExpand]) {
        setCollapsedSections(prev => ({
          ...prev,
          [sectionToExpand]: false
        }));
      }
    };

    expandSectionForActiveItem();
  }, [activeItem, collapsedSections, t]);

  const handleNavigation = (item: { id: string; label: string; icon: React.ElementType; path: string }) => {
    if (item.path) {
      onNavigate(item.path);
    } else {
      setActiveItem(item.id);
    }
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        className="lg:hidden fixed top-4 left-4 z-50 bg-white p-2 rounded-md shadow-md"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
      </button>

      {/* Overlay for mobile */}
      {isOpen && <div className="lg:hidden fixed inset-0 bg-black/30 z-30" onClick={() => setIsOpen(false)}></div>}

      {/* Sidebar */}
      <aside className={`
        fixed top-0 left-0 w-64 h-full bg-white border-r border-gray-100
        p-6 pt-24 lg:pt-6 flex flex-col justify-between transition-transform duration-300 ease-in-out z-40 shadow-lg lg:shadow-none
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex-1 overflow-y-auto scrollbar-hide">
          {/* Logo Section */}
          <div className="flex items-center gap-3 mb-8 px-3">
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-2 rounded-lg shadow-md">
              <Truck className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              ZAWAYA
            </h1>
          </div>

          {/* Navigation Groups */}
          <nav className="space-y-6">
            {navGroups.map((group, groupIndex) => (
              <div key={group.title}>
                <SectionHeader
                  title={group.title}
                  isCollapsed={collapsedSections[group.title]}
                  onToggle={() => toggleSection(group.title)}
                />
                {!collapsedSections[group.title] && (
                  <ul className="space-y-1">
                    {group.items.map((item) => (
                      <NavItem
                        key={item.id}
                        icon={item.icon}
                        label={item.label}
                        active={activeItem === item.id}
                        onClick={() => handleNavigation(item)}
                      />
                    ))}
                  </ul>
                )}
                {/* Add visual separator between groups except for the last one */}
                {groupIndex < navGroups.length - 1 && (
                  <div className="mt-4 pt-2 border-b border-gray-100 relative">
                    <div className="absolute inset-x-4 top-0 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>
                  </div>
                )}
              </div>
            ))}
          </nav>
        </div>
        {/* Bottom Section */}
        <div className="border-t border-gray-100 pt-4 mt-4">
          <SectionHeader
            title="Account"
            isCollapsed={collapsedSections['Account']}
            onToggle={() => toggleSection('Account')}
          />
          {!collapsedSections['Account'] && (
            <ul className="space-y-1">
              <NavItem
                icon={Settings}
                label={t('settings')}
                onClick={() => onOpenSettings ? onOpenSettings() : setActiveItem('settings')}
                active={activeItem === 'settings'}
              />
              <li className="mb-1">
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left transition-all duration-200 text-red-600 hover:bg-red-50 hover:text-red-700 hover:transform hover:scale-[1.01]"
                >
                  <LogOut className="w-5 h-5" />
                  <span className="font-medium">{auth('logout')}</span>
                </button>
              </li>
            </ul>
          )}
        </div>
      </aside>
    </>
  );
};

export default Sidebar;

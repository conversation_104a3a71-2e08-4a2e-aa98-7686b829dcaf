'use client';

import React, { useState, useEffect } from 'react';

import { useSettings } from '@/contexts/SettingsContext';
import { useLocale } from '@/contexts/LocaleContext';
import { useNotificationContext } from '@/contexts/NotificationContext';
import { useSettingsTranslations, useCommonTranslations } from '@/hooks/useTranslations';
import {
  User,
  Bell,
  Shield,
  Palette,
  Save,
  Eye,
  EyeOff,
  Moon,
  Sun,
  Monitor,
  Globe
} from 'lucide-react';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type SettingsTab = 'profile' | 'notifications' | 'security' | 'appearance';

export const SettingsModal: React.FC<SettingsModalProps> = ({
  isOpen,
  onClose
}) => {
  const { settings, updateSettings } = useSettings();
  const { locale, changeLocale } = useLocale();
  const { permission, requestPermission, isSupported } = useNotificationContext();
  const t = useSettingsTranslations();
  const common = useCommonTranslations();
  const [activeTab, setActiveTab] = useState<SettingsTab>('profile');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Form states - initialized from settings context
  const [profileData, setProfileData] = useState({
    displayName: '',
    email: '',
    phone: '',
    department: 'Administration',
    role: 'Manager'
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    orderUpdates: true,
    systemAlerts: true,
    weeklyReports: false
  });

  const [securitySettings, setSecuritySettings] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    twoFactorEnabled: false,
    sessionTimeout: '30'
  });

  const [appearanceSettings, setAppearanceSettings] = useState({
    theme: 'light' as 'light' | 'dark' | 'system',
    language: locale,
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    currency: 'EGP'
  });

  // Update form states when settings change
  useEffect(() => {
    if (settings) {
      setProfileData({
        displayName: settings.displayName,
        email: settings.email,
        phone: settings.phone,
        department: settings.department,
        role: settings.role
      });

      setNotificationSettings({
        emailNotifications: settings.emailNotifications,
        pushNotifications: settings.pushNotifications,
        orderUpdates: settings.orderUpdates,
        systemAlerts: settings.systemAlerts,
        weeklyReports: settings.weeklyReports
      });

      setSecuritySettings(prev => ({
        ...prev,
        twoFactorEnabled: settings.twoFactorEnabled,
        sessionTimeout: settings.sessionTimeout
      }));

      setAppearanceSettings({
        theme: settings.theme,
        language: locale, // Always use current locale from context
        dateFormat: settings.dateFormat,
        timeFormat: settings.timeFormat,
        currency: settings.currency
      });
    }
  }, [settings, locale]);

  // Ensure language setting always reflects current locale
  useEffect(() => {
    setAppearanceSettings(prev => ({
      ...prev,
      language: locale
    }));
  }, [locale]);

  if (!isOpen) return null;

  const tabs = [
    { id: 'profile', label: t('profile'), icon: User },
    { id: 'notifications', label: t('notifications'), icon: Bell },
    { id: 'security', label: t('security'), icon: Shield },
    { id: 'appearance', label: t('appearance'), icon: Palette }
  ];

  const handleSave = async () => {
    setLoading(true);
    try {
      // Combine all settings
      const allSettings = {
        ...profileData,
        ...notificationSettings,
        ...securitySettings,
        ...appearanceSettings
      };

      // Update settings using context
      await updateSettings(allSettings);

      console.log('Settings saved successfully');
      onClose();
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Profile Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Display Name</label>
                  <input
                    type="text"
                    value={profileData.displayName}
                    onChange={(e) => setProfileData({ ...profileData, displayName: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <input
                    type="email"
                    value={profileData.email}
                    onChange={(e) => setProfileData({ ...profileData, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                  <input
                    type="tel"
                    value={profileData.phone}
                    onChange={(e) => setProfileData({ ...profileData, phone: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    placeholder="+20 XXX XXX XXXX"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                  <select
                    value={profileData.department}
                    onChange={(e) => setProfileData({ ...profileData, department: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="Administration">Administration</option>
                    <option value="Sales">Sales</option>
                    <option value="Operations">Operations</option>
                    <option value="Customer Service">Customer Service</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h3>

              {/* Browser Permission Status */}
              {isSupported && (
                <div className={`p-4 rounded-lg mb-6 ${
                  permission === 'granted'
                    ? 'bg-green-50 border border-green-200'
                    : permission === 'denied'
                    ? 'bg-red-50 border border-red-200'
                    : 'bg-yellow-50 border border-yellow-200'
                }`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className={`font-medium ${
                        permission === 'granted'
                          ? 'text-green-900'
                          : permission === 'denied'
                          ? 'text-red-900'
                          : 'text-yellow-900'
                      }`}>
                        Browser Notifications
                      </h4>
                      <p className={`text-sm mt-1 ${
                        permission === 'granted'
                          ? 'text-green-700'
                          : permission === 'denied'
                          ? 'text-red-700'
                          : 'text-yellow-700'
                      }`}>
                        {permission === 'granted' && 'Push notifications are enabled'}
                        {permission === 'denied' && 'Push notifications are blocked. Please enable them in your browser settings.'}
                        {permission === 'default' && 'Click to enable push notifications'}
                      </p>
                    </div>
                    {permission !== 'granted' && permission !== 'denied' && (
                      <button
                        onClick={requestPermission}
                        className="px-4 py-2 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700 transition-colors"
                      >
                        Enable
                      </button>
                    )}
                  </div>
                </div>
              )}

              <div className="space-y-4">
                {Object.entries(notificationSettings).map(([key, value]) => {
                  const isDisabled = key === 'pushNotifications' && permission !== 'granted';

                  return (
                    <div key={key} className={`flex items-center justify-between p-4 rounded-lg ${
                      isDisabled ? 'bg-gray-100' : 'bg-gray-50'
                    }`}>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className={`font-medium capitalize ${
                            isDisabled ? 'text-gray-500' : 'text-gray-900'
                          }`}>
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </h4>
                          {key === 'pushNotifications' && permission !== 'granted' && (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                              Permission Required
                            </span>
                          )}
                        </div>
                        <p className={`text-sm mt-1 ${
                          isDisabled ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          {key === 'emailNotifications' && 'Receive notifications via email'}
                          {key === 'pushNotifications' && 'Receive push notifications in browser'}
                          {key === 'orderUpdates' && 'Get notified about order status changes'}
                          {key === 'systemAlerts' && 'Receive system maintenance and alerts'}
                          {key === 'weeklyReports' && 'Get weekly performance reports'}
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={value && !isDisabled}
                          disabled={isDisabled}
                          onChange={(e) => {
                            if (key === 'pushNotifications' && e.target.checked && permission !== 'granted') {
                              requestPermission().then((newPermission) => {
                                if (newPermission === 'granted') {
                                  setNotificationSettings({
                                    ...notificationSettings,
                                    [key]: true
                                  });
                                }
                              });
                            } else {
                              setNotificationSettings({
                                ...notificationSettings,
                                [key]: e.target.checked
                              });
                            }
                          }}
                          className="sr-only peer"
                        />
                        <div className={`w-11 h-6 rounded-full peer transition-all ${
                          isDisabled
                            ? 'bg-gray-300 cursor-not-allowed'
                            : 'bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 peer-checked:bg-indigo-600'
                        } peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all`}></div>
                      </label>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Settings</h3>



              {/* Password Change */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Change Password</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={securitySettings.currentPassword}
                      onChange={(e) => setSecuritySettings({ ...securitySettings, currentPassword: e.target.value })}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                    <input
                      type="password"
                      value={securitySettings.newPassword}
                      onChange={(e) => setSecuritySettings({ ...securitySettings, newPassword: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Confirm Password</label>
                    <input
                      type="password"
                      value={securitySettings.confirmPassword}
                      onChange={(e) => setSecuritySettings({ ...securitySettings, confirmPassword: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    />
                  </div>
                </div>
              </div>

              {/* Session Settings */}
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Session Settings</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                  <select
                    value={securitySettings.sessionTimeout}
                    onChange={(e) => setSecuritySettings({ ...securitySettings, sessionTimeout: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="15">15 minutes</option>
                    <option value="30">30 minutes</option>
                    <option value="60">1 hour</option>
                    <option value="120">2 hours</option>
                    <option value="0">Never</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        );

      case 'appearance':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('appearance')}</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Theme */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">{t('theme')}</label>
                  <div className="space-y-2">
                    {[
                      { value: 'light', label: t('themes.light'), icon: Sun },
                      { value: 'dark', label: t('themes.dark'), icon: Moon },
                      { value: 'system', label: t('themes.system'), icon: Monitor }
                    ].map(({ value, label, icon: Icon }) => (
                      <label key={value} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input
                          type="radio"
                          name="theme"
                          value={value}
                          checked={appearanceSettings.theme === value}
                          onChange={(e) => setAppearanceSettings({ ...appearanceSettings, theme: e.target.value as 'light' | 'dark' | 'system' })}
                          className="text-indigo-600 focus:ring-indigo-500"
                        />
                        <Icon className="w-4 h-4 text-gray-600" />
                        <span className="text-sm font-medium text-gray-900">{label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Language */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Globe className="w-4 h-4 inline mr-2" />
                    {t('language')}
                  </label>
                  <select
                    value={appearanceSettings.language}
                    onChange={async (e) => {
                      const newLanguage = e.target.value as 'en' | 'ar';
                      setAppearanceSettings({ ...appearanceSettings, language: newLanguage });
                      await changeLocale(newLanguage);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="en">{t('languages.en')}</option>
                    <option value="ar">{t('languages.ar')}</option>
                  </select>
                </div>

                {/* Date Format */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">{t('dateFormat')}</label>
                  <select
                    value={appearanceSettings.dateFormat}
                    onChange={(e) => setAppearanceSettings({ ...appearanceSettings, dateFormat: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                  </select>
                </div>

                {/* Time Format */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">{t('timeFormat')}</label>
                  <select
                    value={appearanceSettings.timeFormat}
                    onChange={(e) => setAppearanceSettings({ ...appearanceSettings, timeFormat: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="12h">12 Hour (AM/PM)</option>
                    <option value="24h">24 Hour</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        );



      default:
        return null;
    }
  };

  // Handle click on the backdrop to close the modal
  const handleBackdropClick = (e: React.MouseEvent) => {
    // Only close if clicking the backdrop, not the modal content
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">{t('title')}</h2>
          </div>
        </div>

        <div className="flex h-[calc(90vh-8rem)]">
          {/* Sidebar */}
          <div className="w-64 bg-gray-50 border-r border-gray-100 p-4">
            <nav className="space-y-2">
              {tabs.map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setActiveTab(id as SettingsTab)}
                  className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors ${
                    activeTab === id
                      ? 'bg-indigo-600 text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  {label}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 overflow-y-auto p-6">
              {renderTabContent()}
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-gray-100 bg-gray-50">
              <div className="flex justify-end gap-3">
                <button
                  onClick={onClose}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {common('cancel')}
                </button>
                <button
                  onClick={handleSave}
                  disabled={loading}
                  className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {loading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  {common('save')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;

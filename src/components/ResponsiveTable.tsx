import React, { ReactNode } from 'react';

interface Column {
  key: string;
  label: string;
  className?: string;
  mobileLabel?: string; // Optional different label for mobile
  hideOnMobile?: boolean; // Hide this column on mobile
}

interface ResponsiveTableProps<T = unknown> {
  columns: Column[];
  data: T[];
  renderRow: (item: T, index: number) => ReactNode;
  renderMobileCard?: (item: T, index: number) => ReactNode; // Optional mobile card layout
  loading?: boolean;
  emptyState?: ReactNode;
  className?: string;
}

/**
 * A responsive table component that switches to card layout on mobile
 */
export const ResponsiveTable = <T = unknown,>({
  columns,
  data,
  renderRow,
  renderMobileCard,
  loading = false,
  emptyState,
  className = ''
}: ResponsiveTableProps<T>) => {
  // Desktop loading skeleton component - used inside tbody
  const DesktopLoadingSkeleton = () => (
    <>
      {Array.from({ length: 3 }).map((_, index) => (
        <tr key={`loading-${index}`} className="animate-pulse">
          {columns.map((column) => (
            <td key={column.key} className="py-4 px-6">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </td>
          ))}
        </tr>
      ))}
    </>
  );
  
  // Mobile loading skeleton component - used outside table
  const MobileLoadingSkeleton = () => (
    <div className="md:hidden space-y-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={`mobile-loading-${index}`} className="bg-white p-4 rounded-lg border animate-pulse">
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/3"></div>
          </div>
        </div>
      ))}
    </div>
  );

  const EmptyState = () => (
    <div className="text-center py-12">
      {emptyState || (
        <div className="text-gray-500">
          <p className="text-sm font-medium">No data available</p>
          <p className="text-xs mt-1">Data will appear here when available</p>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className={`bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden ${className}`}>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white hidden md:table">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={`py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.className || ''}`}
                  >
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              <DesktopLoadingSkeleton />
            </tbody>
          </table>
        </div>
        <MobileLoadingSkeleton />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden ${className}`}>
        <EmptyState />
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden ${className}`}>
      {/* Desktop Table View */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full bg-white">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.className || ''}`}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => renderRow(item, index))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden">
        {renderMobileCard ? (
          <div className="space-y-4 p-4">
            {data.map((item, index) => renderMobileCard(item, index))}
          </div>
        ) : (
          <div className="space-y-4 p-4">
            {data.map((item, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg border">
                <div className="space-y-2">
                  {columns
                    .filter(column => !column.hideOnMobile)
                    .map((column) => (
                      <div key={column.key} className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600">
                          {column.mobileLabel || column.label}:
                        </span>
                        <span className="text-sm text-gray-900">
                          {(item as Record<string, unknown>)[column.key] as string || '-'}
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Responsive table row component for desktop view
 */
export const ResponsiveTableRow: React.FC<{
  children: ReactNode;
  onClick?: () => void;
  className?: string;
}> = ({ children, onClick, className = '' }) => {
  return (
    <tr 
      className={`border-b border-gray-100 hover:bg-gray-50 transition-colors ${onClick ? 'cursor-pointer' : ''} ${className}`}
      onClick={onClick}
    >
      {children}
    </tr>
  );
};

/**
 * Responsive table cell component
 */
export const ResponsiveTableCell: React.FC<{
  children: ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <td className={`py-4 px-6 text-sm ${className}`}>
      {children}
    </td>
  );
};

'use client';

import { useEffect } from 'react';
import { useLocale } from '@/contexts/LocaleContext';

export default function LocaleHtmlAttributes() {
  const { locale } = useLocale();

  useEffect(() => {
    // Update the HTML element's lang and dir attributes - FORCE LTR FOR ALL
    const htmlElement = document.documentElement;
    const bodyElement = document.body;

    // Remove any existing direction classes and attributes
    htmlElement.classList.remove('rtl', 'ltr');
    bodyElement.classList.remove('rtl', 'ltr');

    // Set new attributes and styles - FORCE LTR
    htmlElement.lang = locale;
    htmlElement.setAttribute('lang', locale);
    htmlElement.setAttribute('dir', 'ltr'); // Force LTR
    bodyElement.setAttribute('dir', 'ltr'); // Force LTR

    // Add CSS class for direction - FORCE LTR
    htmlElement.classList.add('ltr');
    bodyElement.classList.add('ltr');
  }, [locale]);

  return null;
}

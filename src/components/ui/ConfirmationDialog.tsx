'use client';

import React from 'react';
import { X, AlertTriangle, CheckCircle, Info, AlertCircle } from 'lucide-react';
import { useLocale } from 'next-intl';
import { useCommonTranslations } from '@/hooks/useTranslations';

export type ConfirmationType = 'warning' | 'danger' | 'success' | 'info';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: ConfirmationType;
  icon?: React.ReactNode;
  showCancel?: boolean; // New prop to control whether to show cancel button
}

const typeConfig = {
  warning: {
    icon: AlertTriangle,
    bgColor: 'bg-gradient-to-br from-amber-50 to-orange-50',
    iconColor: 'text-amber-600',
    confirmBg: 'bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600',
    borderColor: 'border-amber-200'
  },
  danger: {
    icon: Alert<PERSON>ircle,
    bgColor: 'bg-gradient-to-br from-red-50 to-pink-50',
    iconColor: 'text-red-600',
    confirmBg: 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600',
    borderColor: 'border-red-200'
  },
  success: {
    icon: CheckCircle,
    bgColor: 'bg-gradient-to-br from-green-50 to-emerald-50',
    iconColor: 'text-green-600',
    confirmBg: 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600',
    borderColor: 'border-green-200'
  },
  info: {
    icon: Info,
    bgColor: 'bg-gradient-to-br from-blue-50 to-indigo-50',
    iconColor: 'text-blue-600',
    confirmBg: 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600',
    borderColor: 'border-blue-200'
  }
};

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText,
  cancelText,
  type = 'warning',
  icon,
  showCancel = true
}) => {
  const locale = useLocale();
  const isRTL = locale === 'ar';
  const common = useCommonTranslations();
  
  const config = typeConfig[type];
  const IconComponent = config.icon;

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <div 
      className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4 backdrop-blur-sm animate-fadeIn"
      onClick={handleBackdropClick}
    >
      <div className={`bg-white rounded-3xl p-0 max-w-md w-full shadow-2xl animate-scaleIn relative overflow-hidden ${config.borderColor} border-2`}>
        {/* Gradient header */}
        <div className={`${config.bgColor} px-6 py-4 border-b ${config.borderColor}`}>
          <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`p-3 rounded-full bg-white/80 ${config.iconColor}`}>
              {icon || <IconComponent className="w-6 h-6" />}
            </div>
            <div className="flex-1">
              <h3 className={`text-lg font-bold text-gray-900 ${isRTL ? 'text-right' : 'text-left'}`} dir={isRTL ? 'rtl' : 'ltr'}>{title}</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-white/50"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className={`text-gray-700 text-base leading-relaxed mb-6 ${isRTL ? 'text-right' : 'text-left'}`} dir={isRTL ? 'rtl' : 'ltr'}>
            {message}
          </p>

          {/* Action buttons */}
          <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''} ${!showCancel ? 'justify-center' : ''}`}>
            {showCancel && (
              <button
                onClick={onClose}
                className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all duration-200 hover:border-gray-400"
              >
                {cancelText || common('cancel')}
              </button>
            )}
            <button
              onClick={handleConfirm}
              className={`${showCancel ? 'flex-1' : 'px-8'} px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl ${config.confirmBg}`}
            >
              {confirmText || common('confirm')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Hook for easier usage
export const useConfirmationDialog = () => {
  const [dialog, setDialog] = React.useState<{
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
    confirmText?: string;
    cancelText?: string;
    type?: ConfirmationType;
    icon?: React.ReactNode;
    showCancel?: boolean;
  }>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {}
  });

  const showConfirmation = (options: Omit<typeof dialog, 'isOpen'>) => {
    setDialog({ ...options, isOpen: true });
  };

  const hideConfirmation = () => {
    setDialog(prev => ({ ...prev, isOpen: false }));
  };

  const ConfirmationDialogComponent = () => (
    <ConfirmationDialog
      isOpen={dialog.isOpen}
      onClose={hideConfirmation}
      onConfirm={dialog.onConfirm}
      title={dialog.title}
      message={dialog.message}
      confirmText={dialog.confirmText}
      cancelText={dialog.cancelText}
      type={dialog.type}
      icon={dialog.icon}
      showCancel={dialog.showCancel}
    />
  );

  return {
    showConfirmation,
    hideConfirmation,
    ConfirmationDialogComponent
  };
};

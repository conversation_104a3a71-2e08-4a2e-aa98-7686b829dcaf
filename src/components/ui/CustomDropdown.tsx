'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check } from 'lucide-react';

export interface DropdownOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
  description?: string;
  disabled?: boolean;
}

interface CustomDropdownProps {
  options: DropdownOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  className?: string;
  label?: string;
  required?: boolean;
}

export const CustomDropdown: React.FC<CustomDropdownProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select an option...',
  disabled = false,
  error,
  className = '',
  label,
  required = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedOption = options.find(option => option.value === value);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown') {
        e.preventDefault();
        setIsOpen(true);
        setFocusedIndex(0);
      }
      return;
    }

    switch (e.key) {
      case 'Escape':
        setIsOpen(false);
        setFocusedIndex(-1);
        break;
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev < options.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev > 0 ? prev - 1 : options.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && options[focusedIndex]) {
          handleSelect(options[focusedIndex].value);
        }
        break;
    }
  };

  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setFocusedIndex(-1);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      
      <div
        className={`
          relative cursor-pointer transition-all duration-300 ease-out
          bg-gradient-to-br from-white via-gray-50 to-white
          border border-gray-200 rounded-2xl px-4 py-3
          shadow-sm hover:shadow-md
          ${disabled ? 'opacity-50 cursor-not-allowed bg-gray-50' : 'hover:border-gray-300'}
          ${isOpen ? 'ring-2 ring-indigo-500 ring-opacity-20 border-indigo-500 shadow-lg' : ''}
          ${error ? 'border-red-500 ring-2 ring-red-500 ring-opacity-20' : ''}
        `}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-controls={`dropdown-list-${Math.random().toString(36).substr(2, 9)}`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {selectedOption?.icon && (
              <span className="flex-shrink-0 text-lg">{selectedOption.icon}</span>
            )}
            <span className={`truncate font-medium ${selectedOption ? 'text-gray-900' : 'text-gray-500'}`}>
              {selectedOption ? selectedOption.label : placeholder}
            </span>
          </div>
          
          <ChevronDown 
            className={`w-5 h-5 text-gray-400 transition-all duration-300 flex-shrink-0 ml-2 ${
              isOpen ? 'rotate-180 text-indigo-500' : ''
            }`} 
          />
        </div>
      </div>

      {/* Enhanced Dropdown List */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-2xl shadow-2xl max-h-64 overflow-hidden animate-dropdown-slide-down">
          {/* Gradient overlay at top */}
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-t-2xl"></div>
          
          <div className="max-h-60 overflow-y-auto custom-scrollbar">
            {options.length === 0 ? (
              <div className="px-4 py-6 text-center text-gray-500">
                <div className="text-gray-300 mb-2">📭</div>
                <p className="text-sm">No options available</p>
              </div>
            ) : (
              options.map((option, index) => (
                <div
                  key={option.value}
                  className={`
                    px-4 py-3 cursor-pointer transition-all duration-200 ease-out
                    flex items-center justify-between group
                    ${option.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50'}
                    ${focusedIndex === index ? 'bg-gradient-to-r from-indigo-50 to-purple-50' : ''}
                    ${option.value === value ? 'bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700' : 'text-gray-900'}
                    ${index === 0 ? 'pt-4' : ''}
                    ${index === options.length - 1 ? 'pb-4' : ''}
                  `}
                  onClick={() => !option.disabled && handleSelect(option.value)}
                  role="option"
                  aria-selected={option.value === value}
                  onMouseEnter={() => setFocusedIndex(index)}
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    {option.icon && (
                      <span className="flex-shrink-0 text-lg group-hover:scale-110 transition-transform duration-200">
                        {option.icon}
                      </span>
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="truncate font-medium group-hover:translate-x-1 transition-transform duration-200">
                        {option.label}
                      </div>
                      {option.description && (
                        <div className="text-sm text-gray-500 truncate group-hover:text-gray-600 transition-colors duration-200">
                          {option.description}
                        </div>
                      )}
                    </div>
                  </div>
                  {option.value === value && (
                    <Check className="w-5 h-5 text-indigo-600 flex-shrink-0 animate-check-bounce" />
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {error && (
        <p className="mt-1 text-sm text-red-600 animate-shake">{error}</p>
      )}
    </div>
  );
};

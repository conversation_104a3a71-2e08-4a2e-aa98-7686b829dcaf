'use client';

import React from 'react';
import { X, AlertTriangle, CheckCircle, Info, AlertCircle, Zap } from 'lucide-react';

import { useCommonTranslations } from '@/hooks/useTranslations';

export type AlertType = 'success' | 'error' | 'warning' | 'info' | 'critical';

interface AlertDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  type?: AlertType;
  icon?: React.ReactNode;
  buttonText?: string;
  showCloseButton?: boolean;
  onButtonClick?: () => void;
}

const alertConfig = {
  success: {
    icon: CheckCircle,
    bgColor: 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-50',
    iconBg: 'bg-gradient-to-br from-green-500 to-emerald-500',
    iconColor: 'text-white',
    buttonBg: 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600',
    borderColor: 'border-green-200',
    glowColor: 'shadow-green-500/20'
  },
  error: {
    icon: AlertCircle,
    bgColor: 'bg-gradient-to-br from-red-50 via-pink-50 to-red-50',
    iconBg: 'bg-gradient-to-br from-red-500 to-pink-500',
    iconColor: 'text-white',
    buttonBg: 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600',
    borderColor: 'border-red-200',
    glowColor: 'shadow-red-500/20'
  },
  warning: {
    icon: AlertTriangle,
    bgColor: 'bg-gradient-to-br from-amber-50 via-orange-50 to-amber-50',
    iconBg: 'bg-gradient-to-br from-amber-500 to-orange-500',
    iconColor: 'text-white',
    buttonBg: 'bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600',
    borderColor: 'border-amber-200',
    glowColor: 'shadow-amber-500/20'
  },
  info: {
    icon: Info,
    bgColor: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50',
    iconBg: 'bg-gradient-to-br from-blue-500 to-indigo-500',
    iconColor: 'text-white',
    buttonBg: 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600',
    borderColor: 'border-blue-200',
    glowColor: 'shadow-blue-500/20'
  },
  critical: {
    icon: Zap,
    bgColor: 'bg-gradient-to-br from-purple-50 via-violet-50 to-purple-50',
    iconBg: 'bg-gradient-to-br from-purple-500 to-violet-500',
    iconColor: 'text-white',
    buttonBg: 'bg-gradient-to-r from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600',
    borderColor: 'border-purple-200',
    glowColor: 'shadow-purple-500/20'
  }
};

export const AlertDialog: React.FC<AlertDialogProps> = ({
  isOpen,
  onClose,
  title,
  message,
  type = 'info',
  icon,
  buttonText,
  showCloseButton = true,
  onButtonClick
}) => {


  const common = useCommonTranslations();
  
  const config = alertConfig[type];
  const IconComponent = config.icon;

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && showCloseButton) {
      onClose();
    }
  };

  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick();
    } else {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4 backdrop-blur-sm animate-fadeIn"
      onClick={handleBackdropClick}
    >
      <div className={`
        bg-white rounded-3xl p-0 max-w-md w-full shadow-2xl animate-scaleIn relative overflow-hidden 
        ${config.borderColor} border-2 ${config.glowColor} shadow-2xl
      `}>
        {/* Gradient header with icon */}
        <div className={`${config.bgColor} px-6 py-6 border-b ${config.borderColor} relative`}>
          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
          
          <div className="flex flex-col items-center text-center">
            {/* Icon with glow effect */}
            <div className={`
              p-4 rounded-full ${config.iconBg} ${config.iconColor} mb-4
              shadow-lg ${config.glowColor} animate-pulse-slow
            `}>
              {icon || <IconComponent className="w-8 h-8" />}
            </div>
            
            <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
            
            {showCloseButton && (
              <button
                onClick={onClose}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors p-2 rounded-full hover:bg-white/50"
              >
                <X className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 text-center">
          <p className="text-gray-700 text-base leading-relaxed mb-6">
            {message}
          </p>

          {/* Action button */}
          <button
            onClick={handleButtonClick}
            className={`
              w-full px-6 py-3 text-white rounded-xl font-medium 
              transition-all duration-200 transform hover:scale-105 
              shadow-lg hover:shadow-xl ${config.buttonBg}
            `}
          >
            {buttonText || common('close')}
          </button>
        </div>
      </div>
    </div>
  );
};

// Hook for easier usage
export const useAlertDialog = () => {
  const [dialog, setDialog] = React.useState<{
    isOpen: boolean;
    title: string;
    message: string;
    type?: AlertType;
    icon?: React.ReactNode;
    buttonText?: string;
    showCloseButton?: boolean;
    onButtonClick?: () => void;
  }>({
    isOpen: false,
    title: '',
    message: ''
  });

  const showAlert = (options: Omit<typeof dialog, 'isOpen'>) => {
    setDialog({ ...options, isOpen: true });
  };

  const hideAlert = () => {
    setDialog(prev => ({ ...prev, isOpen: false }));
  };

  const AlertDialogComponent = () => (
    <AlertDialog
      isOpen={dialog.isOpen}
      onClose={hideAlert}
      title={dialog.title}
      message={dialog.message}
      type={dialog.type}
      icon={dialog.icon}
      buttonText={dialog.buttonText}
      showCloseButton={dialog.showCloseButton}
      onButtonClick={dialog.onButtonClick}
    />
  );

  return {
    showAlert,
    hideAlert,
    AlertDialogComponent
  };
};

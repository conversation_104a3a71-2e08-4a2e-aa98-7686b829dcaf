'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from '@/contexts/LocaleContext';

/**
 * Hook for locale-aware navigation
 * Automatically adds the current locale prefix to navigation paths
 */
export const useLocaleAwareNavigation = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { locale } = useLocale();

  /**
   * Helper function to ensure path has locale prefix
   */
  const getLocaleAwarePath = (path: string) => {
    // If path already has a locale prefix, return as is
    if (path.match(/^\/[a-z]{2}(\/|$)/)) {
      return path;
    }

    // If path doesn't start with /, add it
    if (!path.startsWith('/')) {
      path = '/' + path;
    }

    // Use current locale from context, fallback to URL locale to maintain consistency
    const currentLocale = locale || pathname.split('/')[1] || 'en';
    return `/${currentLocale}${path}`;
  };

  /**
   * Navigate to a path with automatic locale prefix
   */
  const navigate = (path: string) => {
    const localeAwarePath = getLocaleAwarePath(path);
    router.push(localeAwarePath);
  };

  /**
   * Replace current path with automatic locale prefix
   */
  const replace = (path: string) => {
    const localeAwarePath = getLocaleAwarePath(path);
    router.replace(localeAwarePath);
  };

  /**
   * Get current path without locale prefix
   */
  const getCurrentPathWithoutLocale = () => {
    return pathname.replace(/^\/[a-z]{2}/, '') || '/';
  };

  return {
    navigate,
    replace,
    getLocaleAwarePath,
    getCurrentPathWithoutLocale,
    router,
    pathname,
    locale
  };
};

export default useLocaleAwareNavigation;

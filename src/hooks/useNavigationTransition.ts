'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLocale } from '@/contexts/LocaleContext';

interface NavigationTransitionOptions {
  exitDelay?: number;
  animationType?: 'fade' | 'slide';
}

export const useNavigationTransition = (options: NavigationTransitionOptions = {}) => {
  const router = useRouter();
  const { locale } = useLocale();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const { exitDelay = 300, animationType = 'fade' } = options;

  // Helper function to ensure path has locale prefix
  const getLocaleAwarePath = (path: string) => {
    // If path already has a locale prefix, return as is
    if (path.match(/^\/[a-z]{2}(\/|$)/)) {
      return path;
    }

    // If path doesn't start with /, add it
    if (!path.startsWith('/')) {
      path = '/' + path;
    }

    // Use current locale from context, fallback to URL locale
    const currentLocale = locale || window.location.pathname.split('/')[1] || 'en';
    return `/${currentLocale}${path}`;
  };

  const navigateWithTransition = async (path: string) => {
    if (isTransitioning) return; // Prevent multiple transitions

    setIsTransitioning(true);

    // Add transition class to body to prevent interactions during transition
    document.body.classList.add('nav-transitioning');

    // Add exit animation class to main content
    const mainContent = document.querySelector('[data-page-content]');
    if (mainContent) {
      mainContent.classList.add(animationType === 'slide' ? 'page-slide-exit' : 'page-exit');
    }

    // Wait for exit animation to complete
    await new Promise(resolve => setTimeout(resolve, exitDelay));

    // Navigate to new page with locale-aware path
    const localeAwarePath = getLocaleAwarePath(path);
    router.push(localeAwarePath);

    // Clean up after a short delay (the new page will handle enter animation)
    setTimeout(() => {
      setIsTransitioning(false);
      document.body.classList.remove('nav-transitioning');
    }, 100);
  };

  return {
    isTransitioning,
    navigateWithTransition,
    getLocaleAwarePath
  };
};

export default useNavigationTransition;

import { useState, useEffect, useCallback } from 'react';
import { notificationService, NotificationData, NotificationType } from '@/services/notifications/notificationService';
import { useAuth } from '@/contexts/AuthContext';
import { useSettings } from '@/contexts/SettingsContext';

export interface InAppNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  data?: Record<string, unknown>;
}

export interface NotificationState {
  notifications: InAppNotification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
}

export interface UseNotificationsReturn {
  // State
  notifications: InAppNotification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  isSupported: boolean;
  permission: NotificationPermission;
  
  // Actions
  requestPermission: () => Promise<NotificationPermission>;
  sendNotification: (type: NotificationType, data: NotificationData) => Promise<void>;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  
  // Initialization
  initialize: () => Promise<void>;
}

export const useNotifications = (): UseNotificationsReturn => {
  const { user } = useAuth();
  const { settings } = useSettings();
  
  const [state, setState] = useState<NotificationState>({
    notifications: [],
    unreadCount: 0,
    isLoading: false,
    error: null
  });

  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isSupported, setIsSupported] = useState(false);

  // Check if notifications are supported
  useEffect(() => {
    setIsSupported('Notification' in window);
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }
  }, []);

  /**
   * Initialize the notification service
   */
  const initialize = useCallback(async () => {
    if (!user || !isSupported) return;

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      await notificationService.initialize();
      console.log('✅ Notifications initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize notifications:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to initialize notifications' 
      }));
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [user, isSupported]);

  // Initialize notification service when user is available
  useEffect(() => {
    const initializeNotifications = async () => {
      if (!user || !isSupported) return;

      setState(prev => ({ ...prev, isLoading: true, error: null }));

      try {
        await notificationService.initialize();
        console.log('✅ Notifications initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize notifications:', error);
        setState(prev => ({
          ...prev,
          error: error instanceof Error ? error.message : 'Failed to initialize notifications'
        }));
      } finally {
        setState(prev => ({ ...prev, isLoading: false }));
      }
    };

    if (user && settings && isSupported) {
      initializeNotifications();
    }
  }, [user, settings, isSupported]);

  // Update unread count when notifications change
  useEffect(() => {
    const unreadCount = state.notifications.filter(n => !n.read).length;
    setState(prev => ({ ...prev, unreadCount }));
  }, [state.notifications]);

  /**
   * Request notification permission
   */
  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    if (!isSupported) {
      return 'denied';
    }

    try {
      const newPermission = await notificationService.requestPermission();
      setPermission(newPermission);
      
      if (newPermission === 'granted') {
        // Re-initialize service after permission granted
        await initialize();
      }
      
      return newPermission;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return 'denied';
    }
  }, [isSupported, initialize]);

  /**
   * Send a notification
   */
  const sendNotification = useCallback(async (
    type: NotificationType, 
    data: NotificationData
  ): Promise<void> => {
    if (!user) {
      console.warn('Cannot send notification: user not authenticated');
      return;
    }

    try {
      // Add to in-app notifications
      const newNotification: InAppNotification = {
        id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        title: data.title,
        message: data.body,
        timestamp: new Date(),
        read: false,
        data: data.data
      };

      setState(prev => ({
        ...prev,
        notifications: [newNotification, ...prev.notifications].slice(0, 50) // Keep only last 50
      }));

      // Send via notification service
      await notificationService.sendNotification(user.uid, type, data);
      
    } catch (error) {
      console.error('Error sending notification:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to send notification' 
      }));
    }
  }, [user]);

  /**
   * Mark notification as read
   */
  const markAsRead = useCallback((notificationId: string) => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    }));
  }, []);

  /**
   * Mark all notifications as read
   */
  const markAllAsRead = useCallback(() => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.map(n => ({ ...n, read: true }))
    }));
  }, []);

  /**
   * Clear a specific notification
   */
  const clearNotification = useCallback((notificationId: string) => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.filter(n => n.id !== notificationId)
    }));
  }, []);

  /**
   * Clear all notifications
   */
  const clearAllNotifications = useCallback(() => {
    setState(prev => ({
      ...prev,
      notifications: []
    }));
  }, []);

  return {
    // State
    notifications: state.notifications,
    unreadCount: state.unreadCount,
    isLoading: state.isLoading,
    error: state.error,
    isSupported,
    permission,
    
    // Actions
    requestPermission,
    sendNotification,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    
    // Initialization
    initialize
  };
};

export default useNotifications;

'use client';

import { useTranslations as useNextIntlTranslations } from 'next-intl';

// Custom hook that provides type-safe translations
export function useTranslations(namespace?: string) {
  return useNextIntlTranslations(namespace);
}

// Specific hooks for different sections
export function useCommonTranslations() {
  return useNextIntlTranslations('common');
}

export function useAuthTranslations() {
  return useNextIntlTranslations('auth');
}

export function useNavigationTranslations() {
  return useNextIntlTranslations('navigation');
}

export function useDashboardTranslations() {
  return useNextIntlTranslations('dashboard');
}

export function useOrdersTranslations() {
  return useNextIntlTranslations('orders');
}

export function useCustomersTranslations() {
  return useNextIntlTranslations('customers');
}

export function useTeamTranslations() {
  return useNextIntlTranslations('team');
}

export function useFleetTranslations() {
  return useNextIntlTranslations('fleet');
}

export function useReportsTranslations() {
  return useNextIntlTranslations('reports');
}

export function usePurchasingTranslations() {
  return useNextIntlTranslations('purchasing');
}

export function useSettingsTranslations() {
  return useNextIntlTranslations('settings');
}

export function useConfirmationTranslations() {
  return useNextIntlTranslations('confirmations');
}

export function useToastTranslations() {
  return useNextIntlTranslations('toasts');
}

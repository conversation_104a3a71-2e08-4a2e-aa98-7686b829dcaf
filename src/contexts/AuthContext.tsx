'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  GoogleAuthProvider,
  FacebookAuthProvider,
  signInWithPopup,
  sendPasswordResetEmail,
  updateProfile,
  setPersistence,
  browserLocalPersistence,
  browserSessionPersistence
} from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { UserPreferencesService } from '@/services/firebase/userPreferences';
import { RememberedEmailService } from '@/services/localStorage/rememberedEmail';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  signUp: (email: string, password: string, displayName?: string) => Promise<void>;
  logout: () => Promise<void>;
  signInWithGoogle: (rememberMe?: boolean) => Promise<void>;
  signInWithFacebook: (rememberMe?: boolean) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  // Remember me functionality
  getRememberedEmail: () => Promise<string | null>;
  clearRememberedEmail: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string, rememberMe: boolean = false) => {
    try {
      // Set persistence based on remember me choice
      const persistence = rememberMe ? browserLocalPersistence : browserSessionPersistence;
      await setPersistence(auth, persistence);

      // Sign in the user
      const result = await signInWithEmailAndPassword(auth, email, password);

      // Handle remembered email
      if (result.user) {
        if (rememberMe) {
          // Store email in both local storage and Firestore
          RememberedEmailService.setRememberedEmail(email);
          await UserPreferencesService.updateRememberedEmail(result.user.uid, email);
        } else {
          // Clear remembered email from both local storage and Firestore
          RememberedEmailService.clearRememberedEmail();
          await UserPreferencesService.updateRememberedEmail(result.user.uid, null);
        }
      }
    } catch (error) {
      throw error;
    }
  };

  const signUp = async (email: string, password: string, displayName?: string) => {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      if (displayName && result.user) {
        await updateProfile(result.user, { displayName });
      }
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
      // Clear remembered email on logout if session persistence was used
      // (Local persistence will keep the email for next login)
    } catch (error) {
      throw error;
    }
  };

  const signInWithGoogle = async (rememberMe: boolean = false) => {
    try {
      // Set persistence based on remember me choice
      const persistence = rememberMe ? browserLocalPersistence : browserSessionPersistence;
      await setPersistence(auth, persistence);

      const provider = new GoogleAuthProvider();
      const result = await signInWithPopup(auth, provider);

      // Handle remembered email
      if (result.user && result.user.email) {
        if (rememberMe) {
          // Store email in both local storage and Firestore
          RememberedEmailService.setRememberedEmail(result.user.email);
          await UserPreferencesService.updateRememberedEmail(result.user.uid, result.user.email);
        } else {
          // Clear remembered email from both local storage and Firestore
          RememberedEmailService.clearRememberedEmail();
          await UserPreferencesService.updateRememberedEmail(result.user.uid, null);
        }
      }
    } catch (error) {
      throw error;
    }
  };

  const signInWithFacebook = async (rememberMe: boolean = false) => {
    try {
      // Set persistence based on remember me choice
      const persistence = rememberMe ? browserLocalPersistence : browserSessionPersistence;
      await setPersistence(auth, persistence);

      const provider = new FacebookAuthProvider();
      const result = await signInWithPopup(auth, provider);

      // Handle remembered email
      if (result.user && result.user.email) {
        if (rememberMe) {
          // Store email in both local storage and Firestore
          RememberedEmailService.setRememberedEmail(result.user.email);
          await UserPreferencesService.updateRememberedEmail(result.user.uid, result.user.email);
        } else {
          // Clear remembered email from both local storage and Firestore
          RememberedEmailService.clearRememberedEmail();
          await UserPreferencesService.updateRememberedEmail(result.user.uid, null);
        }
      }
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      throw error;
    }
  };



  // Remember me functionality
  const getRememberedEmail = async (): Promise<string | null> => {
    try {
      // For unauthenticated users, get from local storage
      if (!user) {
        return RememberedEmailService.getRememberedEmail();
      }

      // For authenticated users, try to get from Firestore first, fallback to local storage
      const preferences = await UserPreferencesService.getUserPreferences(user.uid);
      const firestoreEmail = preferences?.rememberedEmail || null;

      if (firestoreEmail) {
        // Sync local storage with Firestore
        RememberedEmailService.setRememberedEmail(firestoreEmail);
        return firestoreEmail;
      }

      // Fallback to local storage
      return RememberedEmailService.getRememberedEmail();
    } catch (error) {
      console.error('Error getting remembered email:', error);
      // Fallback to local storage
      return RememberedEmailService.getRememberedEmail();
    }
  };

  const clearRememberedEmail = async (): Promise<void> => {
    try {
      // Clear from local storage
      RememberedEmailService.clearRememberedEmail();

      // Clear from Firestore if user is authenticated
      if (user) {
        await UserPreferencesService.updateRememberedEmail(user.uid, null);
      }
    } catch (error) {
      console.error('Error clearing remembered email:', error);
      // At least clear from local storage
      RememberedEmailService.clearRememberedEmail();
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signUp,
    logout,
    signInWithGoogle,
    signInWithFacebook,
    resetPassword,
    getRememberedEmail,
    clearRememberedEmail,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

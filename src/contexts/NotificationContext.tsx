'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useNotifications, UseNotificationsReturn } from '@/hooks/useNotifications';
import { useAuth } from './AuthContext';
import { realTimeNotificationService } from '@/services/notifications/realTimeNotifications';
import NotificationPermissionModal from '@/components/NotificationPermissionModal';

type NotificationContextType = UseNotificationsReturn;

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const notifications = useNotifications();
  const [showPermissionModal, setShowPermissionModal] = useState(false);

  // Auto-initialize real-time notifications when user is available
  useEffect(() => {
    if (user) {
      // Initialize real-time notifications for order updates
      realTimeNotificationService.initialize(user.uid);
    }

    // Cleanup on unmount or user change
    return () => {
      if (realTimeNotificationService.isServiceInitialized()) {
        realTimeNotificationService.cleanup();
      }
    };
  }, [user]);

  // Show permission modal on first load if not already granted
  useEffect(() => {
    if (
      user &&
      notifications.isSupported &&
      notifications.permission === 'default'
    ) {
      // Check if user has previously declined
      const hasDeclined = localStorage.getItem('notificationPermissionDeclined') === 'true';

      if (!hasDeclined) {
        // Show custom modal after a short delay for better UX
        const timer = setTimeout(() => {
          setShowPermissionModal(true);
        }, 2000); // 2 second delay

        return () => clearTimeout(timer);
      }
    }
  }, [user, notifications]);

  const handleAcceptNotifications = async () => {
    await notifications.requestPermission();
    setShowPermissionModal(false);
  };

  const handleDeclineNotifications = () => {
    setShowPermissionModal(false);
    // Optionally store user preference to not show again
    localStorage.setItem('notificationPermissionDeclined', 'true');
  };

  return (
    <NotificationContext.Provider value={notifications}>
      {children}

      {/* Enhanced Permission Modal */}
      <NotificationPermissionModal
        isOpen={showPermissionModal}
        onClose={() => setShowPermissionModal(false)}
        onAccept={handleAcceptNotifications}
        onDecline={handleDeclineNotifications}
      />
    </NotificationContext.Provider>
  );
};

export default NotificationProvider;

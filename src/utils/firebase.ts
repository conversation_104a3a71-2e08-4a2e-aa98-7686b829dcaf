/**
 * Firebase utility functions for handling data operations
 */

/**
 * Removes undefined values from an object to prevent Firebase errors
 * Firebase Firestore doesn't allow undefined values in documents
 * @param obj - The object to clean
 * @returns A new object with undefined values removed
 */
export function removeUndefinedValues<T extends Record<string, unknown>>(obj: T): Partial<T> {
  return Object.fromEntries(
    Object.entries(obj).filter(([, value]) => value !== undefined)
  ) as Partial<T>;
}

/**
 * Recursively removes undefined values from nested objects
 * @param obj - The object to clean
 * @returns A new object with undefined values removed at all levels
 */
export function deepRemoveUndefinedValues<T extends Record<string, unknown>>(obj: T): Partial<T> {
  const result: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (value === undefined) {
      continue;
    }
    
    if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
      // Recursively clean nested objects
      const cleanedValue = deepRemoveUndefinedValues(value as Record<string, unknown>);
      if (Object.keys(cleanedValue).length > 0) {
        result[key] = cleanedValue;
      }
    } else if (Array.isArray(value)) {
      // Clean arrays by filtering out undefined values and cleaning nested objects
      const cleanedArray = value
        .filter(item => item !== undefined)
        .map(item => {
          if (item !== null && typeof item === 'object' && !Array.isArray(item)) {
            return deepRemoveUndefinedValues(item as Record<string, unknown>);
          }
          return item;
        });
      
      if (cleanedArray.length > 0) {
        result[key] = cleanedArray;
      }
    } else {
      result[key] = value;
    }
  }
  
  return result as Partial<T>;
}

/**
 * Validates that an object doesn't contain undefined values
 * @param obj - The object to validate
 * @param path - Current path for error reporting (used internally)
 * @throws Error if undefined values are found
 */
export function validateNoUndefinedValues(obj: Record<string, unknown>, path = ''): void {
  for (const [key, value] of Object.entries(obj)) {
    const currentPath = path ? `${path}.${key}` : key;
    
    if (value === undefined) {
      throw new Error(`Undefined value found at path: ${currentPath}`);
    }
    
    if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
      validateNoUndefinedValues(value as Record<string, unknown>, currentPath);
    } else if (Array.isArray(value)) {
      value.forEach((item, index) => {
        if (item === undefined) {
          throw new Error(`Undefined value found at path: ${currentPath}[${index}]`);
        }
        if (item !== null && typeof item === 'object' && !Array.isArray(item)) {
          validateNoUndefinedValues(item as Record<string, unknown>, `${currentPath}[${index}]`);
        }
      });
    }
  }
}

/**
 * Safely prepares data for Firebase operations by removing undefined values
 * and optionally validating the result
 * @param data - The data to prepare
 * @param validate - Whether to validate the result (default: false)
 * @returns Clean data ready for Firebase
 */
export function prepareForFirebase<T extends Record<string, unknown>>(
  data: T,
  validate = false
): Partial<T> {
  const cleanData = removeUndefinedValues(data);
  
  if (validate) {
    validateNoUndefinedValues(cleanData);
  }
  
  return cleanData;
}

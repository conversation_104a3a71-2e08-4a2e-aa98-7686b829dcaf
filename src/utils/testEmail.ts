import { EmailService } from '@/services/email/emailService';
import { Order } from '@/services/firebase/orders';
import { Timestamp } from 'firebase/firestore';

/**
 * Test function to verify email notifications work correctly
 * This is for development/testing purposes only
 */
export async function testEmailNotification() {
  // Create a mock order for testing
  const mockOrder: Order = {
    id: 'test-doc-id',
    orderId: 'ORD-TEST-001',
    customerName: '<PERSON>',
    customerPhone: '+20 ************',
    customerAddress: '123 Test Street, Cairo, Egypt',
    orderType: 'Paints',
    totalAmount: 2500,
    depositAmount: 500,
    status: 'Delivered',
    salesRep: '<PERSON>',
    deliveryAgent: '<PERSON>',
    purchasingRep: '<PERSON><PERSON>',
    estimatedDeliveryTime: new Date().toISOString(),
    cancellationReason: undefined,
    driverId: 'driver-001',
    driverName: '<PERSON>',
    truckId: 'truck-001',
    truckLicensePlate: 'ABC-123',
    items: [
      { name: '<PERSON> Paint', quantity: 5, price: 300 },
      { name: 'Paint Brushes', quantity: 10, price: 50 },
      { name: 'Paint Roller', quantity: 3, price: 100 }
    ],
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
    actions: [
      {
        action: 'Pending',
        timestamp: Timestamp.now(),
        user: 'System',
        location: { lat: 30.0444, lng: 31.2357, address: 'Cairo, Egypt' }
      },
      {
        action: 'Delivered',
        timestamp: Timestamp.now(),
        user: 'Hassan Ahmed',
        location: { lat: 30.0444, lng: 31.2357, address: 'Customer Location, Cairo' }
      }
    ]
  };

  console.log('🧪 Testing email notification...');

  try {
    // Test delivered email
    const deliveredResult = await EmailService.sendOrderNotification({
      order: mockOrder,
      recipientEmail: '<EMAIL>',
      type: 'delivered'
    });

    console.log('✅ Delivered email test result:', deliveredResult);

    // Test cancelled email
    const cancelledOrder = { 
      ...mockOrder, 
      status: 'Cancelled' as const,
      cancellationReason: 'Customer requested cancellation'
    };

    const cancelledResult = await EmailService.sendOrderNotification({
      order: cancelledOrder,
      recipientEmail: '<EMAIL>',
      type: 'cancelled'
    });

    console.log('✅ Cancelled email test result:', cancelledResult);

    return { delivered: deliveredResult, cancelled: cancelledResult };
  } catch (error) {
    console.error('❌ Email test failed:', error);
    throw error;
  }
}

/**
 * Test function that can be called from the browser console
 * Usage: Open browser console and run: testEmailFromConsole()
 */
if (typeof window !== 'undefined') {
  (window as unknown as { testEmailFromConsole: () => Promise<unknown> }).testEmailFromConsole = async () => {
    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result = await response.json();
      console.log('Email test result:', result);
      return result;
    } catch (error) {
      console.error('Failed to test email:', error);
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };
}

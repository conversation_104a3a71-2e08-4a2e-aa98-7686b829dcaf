/**
 * Local storage service for handling remembered email functionality
 * This allows us to remember emails for unauthenticated users
 */

const REMEMBERED_EMAIL_KEY = 'zawaya_delivery_remembered_email';

export class RememberedEmailService {
  /**
   * Get the remembered email from local storage
   */
  static getRememberedEmail(): string | null {
    if (typeof window === 'undefined') {
      return null; // SSR safety
    }

    try {
      const rememberedEmail = localStorage.getItem(REMEMBERED_EMAIL_KEY);
      return rememberedEmail;
    } catch (error) {
      console.error('Error getting remembered email from localStorage:', error);
      return null;
    }
  }

  /**
   * Set the remembered email in local storage
   */
  static setRememberedEmail(email: string | null): void {
    if (typeof window === 'undefined') {
      return; // SSR safety
    }

    try {
      if (email) {
        localStorage.setItem(REMEMBERED_EMAIL_KEY, email);
      } else {
        localStorage.removeItem(REMEMBERED_EMAIL_KEY);
      }
    } catch (error) {
      console.error('Error setting remembered email in localStorage:', error);
    }
  }

  /**
   * Clear the remembered email from local storage
   */
  static clearRememberedEmail(): void {
    this.setRememberedEmail(null);
  }

  /**
   * Check if there is a remembered email
   */
  static hasRememberedEmail(): boolean {
    return this.getRememberedEmail() !== null;
  }
}

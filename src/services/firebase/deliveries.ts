import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { prepareForFirebase } from '@/utils/firebase';

export interface Delivery {
  id: string;
  orderId: string;
  address: string;
  driver: string;
  driverPhone: string;
  eta: string;
  status: 'On Time' | 'Delayed' | 'Early' | 'Delivered' | 'Cancelled';
  coordinates: { lat: number; lng: number };
  estimatedDistance: string;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  createdAt: Timestamp;
  updatedAt: Timestamp;
  deliveredAt?: Timestamp;
  notes?: string;
}

const DELIVERIES_COLLECTION = 'deliveries';

export class DeliveriesService {
  // Get all deliveries
  static async getAllDeliveries(): Promise<Delivery[]> {
    try {
      const deliveriesRef = collection(db, DELIVERIES_COLLECTION);
      const q = query(deliveriesRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Delivery));
    } catch (error) {
      console.error('Error fetching deliveries:', error);
      throw error;
    }
  }

  // Get recent deliveries with limit
  static async getRecentDeliveries(limitCount: number): Promise<Delivery[]> {
    try {
      const deliveriesRef = collection(db, DELIVERIES_COLLECTION);
      const q = query(
        deliveriesRef,
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Delivery));
    } catch (error) {
      console.error('Error fetching recent deliveries:', error);
      throw error;
    }
  }

  // Get deliveries by status
  static async getDeliveriesByStatus(status: string): Promise<Delivery[]> {
    try {
      const deliveriesRef = collection(db, DELIVERIES_COLLECTION);
      const q = query(
        deliveriesRef,
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Delivery));
    } catch (error) {
      console.error('Error fetching deliveries by status:', error);
      throw error;
    }
  }

  // Get deliveries by driver
  static async getDeliveriesByDriver(driverName: string): Promise<Delivery[]> {
    try {
      const deliveriesRef = collection(db, DELIVERIES_COLLECTION);
      const q = query(
        deliveriesRef,
        where('driver', '==', driverName),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Delivery));
    } catch (error) {
      console.error('Error fetching deliveries by driver:', error);
      throw error;
    }
  }

  // Get single delivery
  static async getDelivery(id: string): Promise<Delivery | null> {
    try {
      const deliveryRef = doc(db, DELIVERIES_COLLECTION, id);
      const snapshot = await getDoc(deliveryRef);

      if (snapshot.exists()) {
        return {
          id: snapshot.id,
          ...snapshot.data()
        } as Delivery;
      }

      return null;
    } catch (error) {
      console.error('Error fetching delivery:', error);
      throw error;
    }
  }

  // Create new delivery
  static async createDelivery(deliveryData: Omit<Delivery, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const deliveriesRef = collection(db, DELIVERIES_COLLECTION);

      const cleanData = prepareForFirebase({
        ...deliveryData,
        createdAt: now,
        updatedAt: now
      });

      const docRef = await addDoc(deliveriesRef, cleanData);

      return docRef.id;
    } catch (error) {
      console.error('Error creating delivery:', error);
      throw error;
    }
  }

  // Update delivery
  static async updateDelivery(id: string, updates: Partial<Delivery>): Promise<void> {
    try {
      const deliveryRef = doc(db, DELIVERIES_COLLECTION, id);

      const cleanUpdates = prepareForFirebase({
        ...updates,
        updatedAt: Timestamp.now()
      });

      await updateDoc(deliveryRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating delivery:', error);
      throw error;
    }
  }

  // Delete delivery
  static async deleteDelivery(id: string): Promise<void> {
    try {
      const deliveryRef = doc(db, DELIVERIES_COLLECTION, id);
      await deleteDoc(deliveryRef);
    } catch (error) {
      console.error('Error deleting delivery:', error);
      throw error;
    }
  }

  // Mark delivery as delivered
  static async markAsDelivered(id: string): Promise<void> {
    try {
      await this.updateDelivery(id, {
        status: 'Delivered',
        deliveredAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error marking delivery as delivered:', error);
      throw error;
    }
  }

  // Get delivery statistics
  static async getDeliveryStats(): Promise<{
    totalDeliveries: number;
    activeDeliveries: number;
    deliveredToday: number;
    onTimeDeliveries: number;
    delayedDeliveries: number;
  }> {
    try {
      const deliveries = await this.getAllDeliveries();
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const stats = {
        totalDeliveries: deliveries.length,
        activeDeliveries: deliveries.filter(d => !['Delivered', 'Cancelled'].includes(d.status)).length,
        deliveredToday: deliveries.filter(d =>
          d.deliveredAt &&
          d.deliveredAt.toDate() >= today
        ).length,
        onTimeDeliveries: deliveries.filter(d => d.status === 'On Time').length,
        delayedDeliveries: deliveries.filter(d => d.status === 'Delayed').length
      };

      return stats;
    } catch (error) {
      console.error('Error getting delivery stats:', error);
      throw error;
    }
  }
}

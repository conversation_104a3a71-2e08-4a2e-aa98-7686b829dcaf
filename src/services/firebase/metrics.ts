import {
  collection,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { OrdersService } from './orders';
import { CustomersService } from './customers';
import { TeamService } from './team';
import { FleetService } from './fleet';

export interface DashboardMetrics {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  totalCustomers: number;
  activeCustomers: number;
  vipCustomers: number;
  totalTeamMembers: number;
  activeTeamMembers: number;
  totalTrucks: number;
  activeTrucks: number;
  averageDeliveryTime: number; // in hours
  customerSatisfaction: number; // average rating out of 5
  deliverySuccessRate: number; // percentage
  recentOrders: Array<Record<string, unknown>>;
  recentDeliveries: Array<Record<string, unknown>>;
  orderTrends: Array<{
    date: string;
    orders: number;
    revenue: number;
  }>;
}

export class MetricsService {
  // Get comprehensive dashboard metrics
  static async getDashboardMetrics(period: string = '7d'): Promise<DashboardMetrics> {
    try {
      const [
        allOrders,
        customers,
        teamStats,
        fleetStats
      ] = await Promise.all([
        OrdersService.getAllOrders(),
        CustomersService.getAllCustomers(),
        TeamService.getTeamStats(),
        FleetService.getFleetStats()
      ]);

      // Calculate date range based on period
      const now = new Date();
      const startDate = new Date();

      switch (period) {
        case '7d':
          startDate.setDate(now.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(now.getDate() - 30);
          break;
        case '3m':
          startDate.setMonth(now.getMonth() - 3);
          break;
        default:
          startDate.setDate(now.getDate() - 7);
      }

      // Filter orders based on the selected period
      const orders = allOrders.filter(order => {
        const orderDate = order.createdAt.toDate();
        return orderDate >= startDate;
      });

      // Calculate order metrics for the filtered period
      const totalOrders = orders.length;
      const pendingOrders = orders.filter(o => o.status === 'Pending').length;
      const completedOrders = orders.filter(o => o.status === 'Delivered').length;
      const cancelledOrders = orders.filter(o => o.status === 'Cancelled').length;
      const totalRevenue = orders
        .filter(o => o.status === 'Delivered')
        .reduce((sum, order) => sum + order.totalAmount, 0);

      // Calculate customer metrics
      const totalCustomers = customers.length;
      const activeCustomers = customers.filter(c => c.status === 'Active').length;
      const vipCustomers = customers.filter(c => c.isVIP).length;

      // Get recent orders (last 5)
      const recentOrders = orders
        .sort((a, b) => b.createdAt.toMillis() - a.createdAt.toMillis())
        .slice(0, 5)
        .map(order => ({
          id: order.id,
          orderId: order.orderId,
          customerName: order.customerName,
          status: order.status,
          totalAmount: order.totalAmount,
          createdAt: order.createdAt.toDate().toISOString()
        }));

      // Get recent deliveries (completed orders)
      const recentDeliveries = orders
        .filter(o => o.status === 'Delivered')
        .sort((a, b) => b.updatedAt.toMillis() - a.updatedAt.toMillis())
        .slice(0, 4)
        .map(order => ({
          id: order.id,
          orderId: order.orderId,
          customerName: order.customerName,
          deliveryAgent: order.deliveryAgent || 'N/A',
          completedAt: order.updatedAt.toDate().toISOString()
        }));

      // Calculate order trends based on period
      const trendDays = period === '7d' ? 7 : period === '30d' ? 30 : 90;
      const orderTrends = this.calculateOrderTrends(allOrders as unknown as Array<Record<string, unknown>>, trendDays);

      // Calculate delivery metrics
      const deliveryMetrics = this.calculateDeliveryMetrics(orders as unknown as Array<Record<string, unknown>>);

      return {
        totalOrders,
        pendingOrders,
        completedOrders,
        cancelledOrders,
        totalRevenue,
        totalCustomers,
        activeCustomers,
        vipCustomers,
        totalTeamMembers: teamStats.totalMembers,
        activeTeamMembers: teamStats.activeMembers,
        totalTrucks: fleetStats.totalTrucks,
        activeTrucks: fleetStats.activeTrucks,
        averageDeliveryTime: deliveryMetrics.averageDeliveryTime,
        customerSatisfaction: deliveryMetrics.customerSatisfaction,
        deliverySuccessRate: deliveryMetrics.deliverySuccessRate,
        recentOrders,
        recentDeliveries,
        orderTrends
      };
    } catch (error) {
      console.error('Error getting dashboard metrics:', error);
      throw error;
    }
  }

  // Calculate delivery metrics from orders
  private static calculateDeliveryMetrics(orders: Array<Record<string, unknown>>): {
    averageDeliveryTime: number;
    customerSatisfaction: number;
    deliverySuccessRate: number;
  } {
    const deliveredOrders = orders.filter(order => order.status === 'Delivered');
    const totalOrders = orders.length;

    // Calculate delivery success rate
    const deliverySuccessRate = totalOrders > 0 ? Math.round((deliveredOrders.length / totalOrders) * 100) : 0;

    // Calculate average delivery time (mock calculation based on order complexity)
    let totalDeliveryTime = 0;
    let ordersWithDeliveryTime = 0;

    deliveredOrders.forEach(order => {
      // Estimate delivery time based on order type and amount
      const orderAmount = order.totalAmount as number || 0;
      const estimatedTime = this.estimateDeliveryTime(order.orderType as string, orderAmount);
      totalDeliveryTime += estimatedTime;
      ordersWithDeliveryTime++;
    });

    const averageDeliveryTime = ordersWithDeliveryTime > 0 ?
      Math.round((totalDeliveryTime / ordersWithDeliveryTime) * 10) / 10 : 0;

    // Calculate customer satisfaction (mock calculation based on delivery performance)
    const customerSatisfaction = this.calculateCustomerSatisfaction(deliveredOrders, averageDeliveryTime);

    return {
      averageDeliveryTime,
      customerSatisfaction,
      deliverySuccessRate
    };
  }

  // Estimate delivery time based on order characteristics
  private static estimateDeliveryTime(orderType: string, orderAmount: number): number {
    // Base delivery time in hours
    let baseTime = 2.0;

    // Adjust based on order type complexity
    const complexityMultiplier: Record<string, number> = {
      'Paints': 1.0,
      'Electrical Supplies': 1.2,
      'Bathroom Fixtures': 1.5,
      'Plumbing Supplies': 1.3,
      'Gypsum Boards': 1.8,
      'Ceramic Tiles': 1.6,
      'Air Conditioning': 2.0,
      'HDF Flooring': 1.7,
      'TVs': 1.1,
      'Home Appliances': 1.4
    };

    const multiplier = complexityMultiplier[orderType] || 1.0;
    baseTime *= multiplier;

    // Adjust based on order amount (larger orders take longer)
    if (orderAmount > 10000) baseTime *= 1.3;
    else if (orderAmount > 5000) baseTime *= 1.15;

    // Add some realistic variation
    const variation = (Math.random() - 0.5) * 0.8; // ±0.4 hours variation
    return Math.max(0.5, baseTime + variation);
  }

  // Calculate customer satisfaction based on delivery performance
  private static calculateCustomerSatisfaction(deliveredOrders: Array<Record<string, unknown>>, avgDeliveryTime: number): number {
    if (deliveredOrders.length === 0) return 0;

    // Base satisfaction score
    let baseSatisfaction = 4.2;

    // Adjust based on delivery time performance
    if (avgDeliveryTime <= 2.0) baseSatisfaction += 0.6;
    else if (avgDeliveryTime <= 3.0) baseSatisfaction += 0.3;
    else if (avgDeliveryTime > 4.0) baseSatisfaction -= 0.4;

    // Adjust based on delivery success rate
    const successRate = deliveredOrders.length / (deliveredOrders.length + 1); // Simplified
    if (successRate > 0.95) baseSatisfaction += 0.2;
    else if (successRate < 0.85) baseSatisfaction -= 0.3;

    // Add some realistic variation but keep it reasonable
    const variation = (Math.random() - 0.5) * 0.4; // ±0.2 variation
    const finalScore = Math.max(1.0, Math.min(5.0, baseSatisfaction + variation));

    return Math.round(finalScore * 10) / 10; // Round to 1 decimal place
  }

  // Calculate order trends for a given number of days
  private static calculateOrderTrends(orders: Array<Record<string, unknown>>, days: number): Array<{
    date: string;
    orders: number;
    revenue: number;
  }> {
    const trends: Array<{ date: string; orders: number; revenue: number }> = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateString = date.toISOString().split('T')[0];

      const dayOrders = orders.filter(order => {
        const orderDate = (order.createdAt as { toDate: () => Date }).toDate().toISOString().split('T')[0];
        return orderDate === dateString;
      });

      const dayRevenue = dayOrders
        .filter(o => o.status === 'Delivered')
        .reduce((sum, order) => sum + (order.totalAmount as number), 0);

      trends.push({
        date: dateString,
        orders: dayOrders.length,
        revenue: dayRevenue
      });
    }

    return trends;
  }

  // Get metrics for a specific date range
  static async getMetricsForDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<{
    orders: number;
    revenue: number;
    customers: number;
  }> {
    try {
      const startTimestamp = Timestamp.fromDate(startDate);
      const endTimestamp = Timestamp.fromDate(endDate);

      // Get orders in date range
      const ordersRef = collection(db, 'orders');
      const ordersQuery = query(
        ordersRef,
        where('createdAt', '>=', startTimestamp),
        where('createdAt', '<=', endTimestamp),
        orderBy('createdAt', 'desc')
      );
      const ordersSnapshot = await getDocs(ordersQuery);
      const orders = ordersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // Get customers in date range
      const customersRef = collection(db, 'customers');
      const customersQuery = query(
        customersRef,
        where('createdAt', '>=', startTimestamp),
        where('createdAt', '<=', endTimestamp),
        orderBy('createdAt', 'desc')
      );
      const customersSnapshot = await getDocs(customersQuery);

      const revenue = orders
        .filter((o: Record<string, unknown>) => o.status === 'Delivered')
        .reduce((sum: number, order: Record<string, unknown>) => sum + (order.totalAmount as number), 0);

      return {
        orders: orders.length,
        revenue,
        customers: customersSnapshot.docs.length
      };
    } catch (error) {
      console.error('Error getting metrics for date range:', error);
      throw error;
    }
  }

  // Get performance metrics for reports
  static async getPerformanceMetrics(): Promise<{
    ordersByStatus: Record<string, number>;
    revenueByMonth: Array<{ month: string; revenue: number }>;
    topCustomers: Array<{ name: string; totalSpent: number; orders: number }>;
    teamPerformance: Array<{ name: string; role: string; ordersHandled: number }>;
  }> {
    try {
      const [orders, customers, teamMembers] = await Promise.all([
        OrdersService.getAllOrders(),
        CustomersService.getAllCustomers(),
        TeamService.getAllTeamMembers()
      ]);

      // Orders by status
      const ordersByStatus: Record<string, number> = {};
      orders.forEach(order => {
        ordersByStatus[order.status] = (ordersByStatus[order.status] || 0) + 1;
      });

      // Revenue by month (last 6 months)
      const revenueByMonth = this.calculateRevenueByMonth(orders as unknown as Array<Record<string, unknown>>, 6);

      // Top customers by spending
      const topCustomers = customers
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, 10)
        .map(customer => ({
          name: customer.name,
          totalSpent: customer.totalSpent,
          orders: customer.totalOrders
        }));

      // Team performance
      const teamPerformance = teamMembers
        .filter(member => member.performance)
        .sort((a, b) => (b.performance?.ordersHandled || 0) - (a.performance?.ordersHandled || 0))
        .slice(0, 10)
        .map(member => ({
          name: member.name,
          role: member.role,
          ordersHandled: member.performance?.ordersHandled || 0
        }));

      return {
        ordersByStatus,
        revenueByMonth,
        topCustomers,
        teamPerformance
      };
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      throw error;
    }
  }

  // Calculate revenue by month
  private static calculateRevenueByMonth(orders: Array<Record<string, unknown>>, months: number): Array<{
    month: string;
    revenue: number;
  }> {
    const revenueByMonth: Array<{ month: string; revenue: number }> = [];
    const now = new Date();

    for (let i = months - 1; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthString = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });

      const monthOrders = orders.filter(order => {
        const orderDate = (order.createdAt as { toDate: () => Date }).toDate();
        return orderDate.getFullYear() === date.getFullYear() &&
               orderDate.getMonth() === date.getMonth();
      });

      const monthRevenue = monthOrders
        .filter(o => o.status === 'Delivered')
        .reduce((sum, order) => sum + (order.totalAmount as number), 0);

      revenueByMonth.push({
        month: monthString,
        revenue: monthRevenue
      });
    }

    return revenueByMonth;
  }
}

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { prepareForFirebase } from '@/utils/firebase';

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'Sales' | 'Purchasing' | 'Delivery' | 'Customer Service' | 'Administration';
  department: string;
  status: 'Active' | 'Inactive' | 'On Leave';
  joinDate: Timestamp;
  salary?: number;
  performance?: {
    ordersHandled: number;
    customerRating: number;
    completionRate: number;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
  avatar?: string;
  notes?: string;
}

const TEAM_COLLECTION = 'team';

export class TeamService {
  // Get all team members
  static async getAllTeamMembers(): Promise<TeamMember[]> {
    try {
      const teamRef = collection(db, TEAM_COLLECTION);
      const q = query(teamRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as TeamMember));
    } catch (error) {
      console.error('Error fetching team members:', error);
      throw error;
    }
  }

  // Get team members by role
  static async getTeamMembersByRole(role: string): Promise<TeamMember[]> {
    try {
      const teamRef = collection(db, TEAM_COLLECTION);
      const q = query(
        teamRef,
        where('role', '==', role),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as TeamMember));
    } catch (error) {
      console.error('Error fetching team members by role:', error);
      throw error;
    }
  }

  // Get team members by status
  static async getTeamMembersByStatus(status: string): Promise<TeamMember[]> {
    try {
      const teamRef = collection(db, TEAM_COLLECTION);
      const q = query(
        teamRef,
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as TeamMember));
    } catch (error) {
      console.error('Error fetching team members by status:', error);
      throw error;
    }
  }

  // Get single team member
  static async getTeamMember(id: string): Promise<TeamMember | null> {
    try {
      const memberRef = doc(db, TEAM_COLLECTION, id);
      const snapshot = await getDoc(memberRef);

      if (snapshot.exists()) {
        return {
          id: snapshot.id,
          ...snapshot.data()
        } as TeamMember;
      }

      return null;
    } catch (error) {
      console.error('Error fetching team member:', error);
      throw error;
    }
  }

  // Create new team member
  static async createTeamMember(memberData: Omit<TeamMember, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const teamRef = collection(db, TEAM_COLLECTION);

      const cleanData = prepareForFirebase({
        ...memberData,
        createdAt: now,
        updatedAt: now
      });

      const docRef = await addDoc(teamRef, cleanData);

      return docRef.id;
    } catch (error) {
      console.error('Error creating team member:', error);
      throw error;
    }
  }

  // Update team member
  static async updateTeamMember(id: string, updates: Partial<TeamMember>): Promise<void> {
    try {
      const memberRef = doc(db, TEAM_COLLECTION, id);

      // Filter out undefined values
      const cleanUpdates = Object.fromEntries(
        Object.entries(updates).filter(([, value]) => value !== undefined)
      );

      await updateDoc(memberRef, {
        ...cleanUpdates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating team member:', error);
      throw error;
    }
  }

  // Deactivate team member (preserves data integrity)
  static async deactivateTeamMember(id: string): Promise<void> {
    try {
      const memberRef = doc(db, TEAM_COLLECTION, id);
      await updateDoc(memberRef, {
        status: 'Inactive',
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error deactivating team member:', error);
      throw error;
    }
  }

  // Legacy method for backward compatibility - now deactivates instead of deleting
  static async deleteTeamMember(id: string): Promise<void> {
    return this.deactivateTeamMember(id);
  }

  // Update performance metrics
  static async updatePerformance(
    memberId: string,
    performance: Partial<TeamMember['performance']>
  ): Promise<void> {
    try {
      const member = await this.getTeamMember(memberId);
      if (!member) throw new Error('Team member not found');

      const updatedPerformance = {
        ordersHandled: 0,
        customerRating: 0,
        completionRate: 0,
        ...member.performance,
        ...performance
      };

      await this.updateTeamMember(memberId, { performance: updatedPerformance });
    } catch (error) {
      console.error('Error updating performance:', error);
      throw error;
    }
  }

  // Get team statistics
  static async getTeamStats(): Promise<{
    totalMembers: number;
    activeMembers: number;
    membersByRole: Record<string, number>;
    membersByStatus: Record<string, number>;
  }> {
    try {
      const members = await this.getAllTeamMembers();

      const stats = {
        totalMembers: members.length,
        activeMembers: members.filter(m => m.status === 'Active').length,
        membersByRole: {} as Record<string, number>,
        membersByStatus: {} as Record<string, number>
      };

      // Count by role
      members.forEach(member => {
        stats.membersByRole[member.role] = (stats.membersByRole[member.role] || 0) + 1;
        stats.membersByStatus[member.status] = (stats.membersByStatus[member.status] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Error getting team stats:', error);
      throw error;
    }
  }
}

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { prepareForFirebase } from '@/utils/firebase';

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  city?: string;
  status: 'Active' | 'Inactive';
  isVIP: boolean;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: Timestamp;
  joinDate: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  notes?: string;
  avatar?: string;
  preferredPaymentMethod?: string;
  location?: string;
}

const CUSTOMERS_COLLECTION = 'customers';

export class CustomersService {
  // Get all customers
  static async getAllCustomers(): Promise<Customer[]> {
    try {
      const customersRef = collection(db, CUSTOMERS_COLLECTION);
      const q = query(customersRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Customer));
    } catch (error) {
      console.error('Error fetching customers:', error);
      return [];
    }
  }

  // Get customers by status
  static async getCustomersByStatus(status: string): Promise<Customer[]> {
    try {
      const customersRef = collection(db, CUSTOMERS_COLLECTION);
      let q;

      if (status === 'VIP') {
        q = query(
          customersRef,
          where('isVIP', '==', true),
          orderBy('createdAt', 'desc')
        );
      } else {
        q = query(
          customersRef,
          where('status', '==', status),
          orderBy('createdAt', 'desc')
        );
      }

      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Customer));
    } catch (error) {
      console.error('Error fetching customers by status:', error);
      return [];
    }
  }

  // Get single customer
  static async getCustomer(id: string): Promise<Customer | null> {
    try {
      const customerRef = doc(db, CUSTOMERS_COLLECTION, id);
      const snapshot = await getDoc(customerRef);

      if (snapshot.exists()) {
        return {
          id: snapshot.id,
          ...snapshot.data()
        } as Customer;
      }

      return null;
    } catch (error) {
      console.error('Error fetching customer:', error);
      throw error;
    }
  }

  // Get customer by email
  static async getCustomerByEmail(email: string): Promise<Customer | null> {
    try {
      const customersRef = collection(db, CUSTOMERS_COLLECTION);
      const q = query(customersRef, where('email', '==', email));
      const snapshot = await getDocs(q);

      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        } as Customer;
      }

      return null;
    } catch (error) {
      console.error('Error fetching customer by email:', error);
      throw error;
    }
  }

  // Create new customer
  static async createCustomer(customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt' | 'joinDate'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const customersRef = collection(db, CUSTOMERS_COLLECTION);

      const cleanData = prepareForFirebase({
        ...customerData,
        joinDate: now,
        createdAt: now,
        updatedAt: now
      });

      const docRef = await addDoc(customersRef, cleanData);

      return docRef.id;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }

  // Update customer
  static async updateCustomer(id: string, updates: Partial<Customer>): Promise<void> {
    try {
      const customerRef = doc(db, CUSTOMERS_COLLECTION, id);
      const cleanUpdates = prepareForFirebase({
        ...updates,
        updatedAt: Timestamp.now()
      });

      await updateDoc(customerRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  // Deactivate customer (preserves data integrity)
  static async deactivateCustomer(id: string): Promise<void> {
    try {
      const customerRef = doc(db, CUSTOMERS_COLLECTION, id);
      await updateDoc(customerRef, {
        status: 'Inactive',
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error deactivating customer:', error);
      throw error;
    }
  }

  // Legacy method for backward compatibility - now deactivates instead of deleting
  static async deleteCustomer(id: string): Promise<void> {
    return this.deactivateCustomer(id);
  }

  // Toggle VIP status
  static async toggleVIPStatus(id: string): Promise<void> {
    try {
      const customer = await this.getCustomer(id);
      if (!customer) throw new Error('Customer not found');

      await this.updateCustomer(id, { isVIP: !customer.isVIP });
    } catch (error) {
      console.error('Error toggling VIP status:', error);
      throw error;
    }
  }

  // Update customer stats (called when order is placed/completed)
  static async updateCustomerStats(
    customerId: string,
    orderAmount: number,
    isNewOrder: boolean = true
  ): Promise<void> {
    try {
      const customer = await this.getCustomer(customerId);
      if (!customer) throw new Error('Customer not found');

      const updates: Partial<Customer> = {
        totalOrders: customer.totalOrders + (isNewOrder ? 1 : 0),
        totalSpent: customer.totalSpent + orderAmount,
        lastOrderDate: Timestamp.now()
      };

      await this.updateCustomer(customerId, updates);
    } catch (error) {
      console.error('Error updating customer stats:', error);
      throw error;
    }
  }
}

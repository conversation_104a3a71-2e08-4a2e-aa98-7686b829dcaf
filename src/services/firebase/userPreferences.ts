import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  Timestamp,
  FieldValue
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface UserSettings {
  // Profile
  displayName: string;
  email: string;
  phone: string;
  department: string;
  role: string;

  // Notifications
  emailNotifications: boolean;
  pushNotifications: boolean;
  orderUpdates: boolean;
  systemAlerts: boolean;
  weeklyReports: boolean;

  // Security
  twoFactorEnabled: boolean;
  sessionTimeout: string;

  // Appearance
  theme: 'light' | 'dark' | 'system';
  language: string;
  dateFormat: string;
  timeFormat: string;
  currency: string;
}

export interface UserPreferences {
  id: string;
  userId: string;
  settings: UserSettings;
  rememberedEmail?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

const USER_PREFERENCES_COLLECTION = 'userPreferences';

const defaultSettings: UserSettings = {
  displayName: '',
  email: '',
  phone: '',
  department: 'Administration',
  role: 'Manager',
  emailNotifications: true,
  pushNotifications: true,
  orderUpdates: true,
  systemAlerts: true,
  weeklyReports: false,
  twoFactorEnabled: false,
  sessionTimeout: '30',
  theme: 'light',
  language: 'en',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: '24h',
  currency: 'EGP'
};

export class UserPreferencesService {
  // Get user preferences
  static async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const docRef = doc(db, USER_PREFERENCES_COLLECTION, userId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as UserPreferences;
      }
      return null;
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      return null;
    }
  }

  // Create user preferences
  static async createUserPreferences(userId: string, settings: Partial<UserSettings>, rememberedEmail?: string): Promise<void> {
    try {
      const now = Timestamp.now();
      const preferences: Omit<UserPreferences, 'id'> = {
        userId,
        settings: { ...defaultSettings, ...settings },
        createdAt: now,
        updatedAt: now
      };

      // Only add rememberedEmail if it's not undefined
      if (rememberedEmail !== undefined) {
        preferences.rememberedEmail = rememberedEmail;
      }

      await setDoc(doc(db, USER_PREFERENCES_COLLECTION, userId), preferences);
    } catch (error) {
      console.error('Error creating user preferences:', error);
      throw error;
    }
  }

  // Update user settings
  static async updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<void> {
    try {
      const docRef = doc(db, USER_PREFERENCES_COLLECTION, userId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const currentData = docSnap.data() as UserPreferences;
        await updateDoc(docRef, {
          settings: { ...currentData.settings, ...settings },
          updatedAt: Timestamp.now()
        });
      } else {
        // Create new preferences if they don't exist
        await this.createUserPreferences(userId, settings);
      }
    } catch (error) {
      console.error('Error updating user settings:', error);
      throw error;
    }
  }

  // Update remembered email
  static async updateRememberedEmail(userId: string, email: string | null): Promise<void> {
    try {
      const docRef = doc(db, USER_PREFERENCES_COLLECTION, userId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const updates: Record<string, string | Timestamp | FieldValue> = {
          updatedAt: Timestamp.now()
        };

        if (email !== null) {
          updates.rememberedEmail = email;
        } else {
          // Use deleteField to remove the field instead of setting to null
          const { deleteField } = await import('firebase/firestore');
          updates.rememberedEmail = deleteField();
        }

        await updateDoc(docRef, updates);
      } else {
        // Create new preferences if they don't exist
        // Only pass email if it's not null
        await this.createUserPreferences(userId, {}, email || undefined);
      }
    } catch (error) {
      console.error('Error updating remembered email:', error);
      throw error;
    }
  }

  // Get default settings
  static getDefaultSettings(): UserSettings {
    return { ...defaultSettings };
  }
}

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { prepareForFirebase } from '@/utils/firebase';

export interface Order {
  id: string;
  orderId: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  orderType: string;
  totalAmount: number;
  depositAmount: number;
  status: 'Pending' | 'Started' | 'Moved to Supplier' | 'Arrived at Supplier' | 'Moving to Customer' | 'Arrived at Customer' | 'Delivered' | 'Cancelled';
  salesRep: string;
  deliveryAgent?: string;
  purchasingRep?: string;
  estimatedDeliveryTime?: string;
  cancellationReason?: string;
  driverId?: string;
  driverName?: string;
  truckId?: string;
  truckLicensePlate?: string;
  items?: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  actions?: Array<{
    action: string;
    timestamp: Timestamp;
    user: string;
    location?: {
      lat: number;
      lng: number;
      address?: string;
    };
  }>;
}

const ORDERS_COLLECTION = 'orders';

export class OrdersService {
  // Get all orders
  static async getAllOrders(): Promise<Order[]> {
    try {
      const ordersRef = collection(db, ORDERS_COLLECTION);
      const q = query(ordersRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Order));
    } catch (error) {
      console.error('Error fetching orders:', error);
      return [];
    }
  }

  // Get orders by status
  static async getOrdersByStatus(status: string): Promise<Order[]> {
    try {
      const ordersRef = collection(db, ORDERS_COLLECTION);
      const q = query(
        ordersRef,
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Order));
    } catch (error) {
      console.error('Error fetching orders by status:', error);
      return [];
    }
  }

  // Get recent orders
  static async getRecentOrders(limitCount: number = 5): Promise<Order[]> {
    try {
      const ordersRef = collection(db, ORDERS_COLLECTION);
      const q = query(
        ordersRef,
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Order));
    } catch (error) {
      console.error('Error fetching recent orders:', error);
      return [];
    }
  }

  // Get single order by document ID
  static async getOrder(id: string): Promise<Order | null> {
    try {
      const orderRef = doc(db, ORDERS_COLLECTION, id);
      const snapshot = await getDoc(orderRef);

      if (snapshot.exists()) {
        return {
          id: snapshot.id,
          ...snapshot.data()
        } as Order;
      }

      return null;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  // Get single order by orderId field (e.g., "ORD-001")
  static async getOrderByOrderId(orderId: string): Promise<{ order: Order; docId: string } | null> {
    try {
      const ordersRef = collection(db, ORDERS_COLLECTION);
      const q = query(ordersRef, where('orderId', '==', orderId));
      const snapshot = await getDocs(q);

      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        return {
          order: {
            id: doc.id,
            ...doc.data()
          } as Order,
          docId: doc.id
        };
      }

      return null;
    } catch (error) {
      console.error('Error fetching order by orderId:', error);
      throw error;
    }
  }

  // Create new order
  static async createOrder(orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const ordersRef = collection(db, ORDERS_COLLECTION);

      const cleanData = prepareForFirebase({
        ...orderData,
        createdAt: now,
        updatedAt: now
      });

      const docRef = await addDoc(ordersRef, cleanData);

      return docRef.id;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  // Update order
  static async updateOrder(id: string, updates: Partial<Order>): Promise<void> {
    try {
      const orderRef = doc(db, ORDERS_COLLECTION, id);
      const cleanUpdates = prepareForFirebase({
        ...updates,
        updatedAt: Timestamp.now()
      });

      await updateDoc(orderRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating order:', error);
      throw error;
    }
  }

  // Delete order
  static async deleteOrder(id: string): Promise<void> {
    try {
      const orderRef = doc(db, ORDERS_COLLECTION, id);
      await deleteDoc(orderRef);
    } catch (error) {
      console.error('Error deleting order:', error);
      throw error;
    }
  }

  // Add action to order
  static async addOrderAction(
    orderId: string,
    action: string,
    user: string,
    location?: { lat: number; lng: number; address?: string }
  ): Promise<void> {
    try {
      const order = await this.getOrder(orderId);
      if (!order) throw new Error('Order not found');

      const newAction = {
        action,
        timestamp: Timestamp.now(),
        user,
        ...(location && { location })
      };

      const updatedActions = [...(order.actions || []), newAction];

      await this.updateOrder(orderId, { actions: updatedActions });
    } catch (error) {
      console.error('Error adding order action:', error);
      throw error;
    }
  }
}

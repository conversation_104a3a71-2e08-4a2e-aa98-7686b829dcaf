import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  query,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { prepareForFirebase } from '@/utils/firebase';

export interface Truck {
  id: string;
  licensePlate: string;
  model: string;
  year: number;
  capacity: string;
  status: 'Active' | 'Maintenance' | 'Inactive';
  driverId?: string;
  driverName?: string;
  lastMaintenance: string;
  nextMaintenance: string;
  mileage: number;
  fuelType: 'Diesel' | 'Gasoline' | 'Electric';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Driver {
  id: string;
  name: string;
  licenseNumber: string;
  phone: string;
  email: string;
  status: 'Available' | 'On Duty' | 'Off Duty' | 'Inactive';
  truckId?: string;
  truckLicensePlate?: string;
  joinDate: string;
  licenseExpiry: string;
  experience: number; // years
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

const TRUCKS_COLLECTION = 'trucks';
const DRIVERS_COLLECTION = 'drivers';

export class FleetService {
  // TRUCKS
  static async getAllTrucks(): Promise<Truck[]> {
    try {
      const trucksRef = collection(db, TRUCKS_COLLECTION);
      const q = query(trucksRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Truck));
    } catch (error) {
      console.error('Error fetching trucks:', error);
      throw error;
    }
  }

  static async getTruck(id: string): Promise<Truck | null> {
    try {
      const truckRef = doc(db, TRUCKS_COLLECTION, id);
      const snapshot = await getDoc(truckRef);

      if (snapshot.exists()) {
        return {
          id: snapshot.id,
          ...snapshot.data()
        } as Truck;
      }

      return null;
    } catch (error) {
      console.error('Error fetching truck:', error);
      throw error;
    }
  }

  static async createTruck(truckData: Omit<Truck, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const trucksRef = collection(db, TRUCKS_COLLECTION);

      const cleanData = prepareForFirebase({
        ...truckData,
        createdAt: now,
        updatedAt: now
      });

      const docRef = await addDoc(trucksRef, cleanData);

      return docRef.id;
    } catch (error) {
      console.error('Error creating truck:', error);
      throw error;
    }
  }

  static async updateTruck(id: string, updates: Partial<Truck>): Promise<void> {
    try {
      const truckRef = doc(db, TRUCKS_COLLECTION, id);
      const cleanUpdates = prepareForFirebase({
        ...updates,
        updatedAt: Timestamp.now()
      });

      await updateDoc(truckRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating truck:', error);
      throw error;
    }
  }

  // Deactivate truck (preserves data integrity)
  static async deactivateTruck(id: string): Promise<void> {
    try {
      const truckRef = doc(db, TRUCKS_COLLECTION, id);
      await updateDoc(truckRef, {
        status: 'Inactive',
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error deactivating truck:', error);
      throw error;
    }
  }

  // Legacy method for backward compatibility - now deactivates instead of deleting
  static async deleteTruck(id: string): Promise<void> {
    return this.deactivateTruck(id);
  }

  // DRIVERS
  static async getAllDrivers(): Promise<Driver[]> {
    try {
      const driversRef = collection(db, DRIVERS_COLLECTION);
      const q = query(driversRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Driver));
    } catch (error) {
      console.error('Error fetching drivers:', error);
      throw error;
    }
  }

  static async getDriver(id: string): Promise<Driver | null> {
    try {
      const driverRef = doc(db, DRIVERS_COLLECTION, id);
      const snapshot = await getDoc(driverRef);

      if (snapshot.exists()) {
        return {
          id: snapshot.id,
          ...snapshot.data()
        } as Driver;
      }

      return null;
    } catch (error) {
      console.error('Error fetching driver:', error);
      throw error;
    }
  }

  static async createDriver(driverData: Omit<Driver, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const driversRef = collection(db, DRIVERS_COLLECTION);

      const cleanData = prepareForFirebase({
        ...driverData,
        createdAt: now,
        updatedAt: now
      });

      const docRef = await addDoc(driversRef, cleanData);

      return docRef.id;
    } catch (error) {
      console.error('Error creating driver:', error);
      throw error;
    }
  }

  static async updateDriver(id: string, updates: Partial<Driver>): Promise<void> {
    try {
      const driverRef = doc(db, DRIVERS_COLLECTION, id);
      const cleanUpdates = prepareForFirebase({
        ...updates,
        updatedAt: Timestamp.now()
      });

      await updateDoc(driverRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating driver:', error);
      throw error;
    }
  }

  // Deactivate driver (preserves data integrity)
  static async deactivateDriver(id: string): Promise<void> {
    try {
      const driverRef = doc(db, DRIVERS_COLLECTION, id);
      await updateDoc(driverRef, {
        status: 'Inactive',
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error deactivating driver:', error);
      throw error;
    }
  }

  // Legacy method for backward compatibility - now deactivates instead of deleting
  static async deleteDriver(id: string): Promise<void> {
    return this.deactivateDriver(id);
  }

  // FLEET STATISTICS
  static async getFleetStats(): Promise<{
    totalTrucks: number;
    activeTrucks: number;
    totalDrivers: number;
    availableDrivers: number;
    trucksInMaintenance: number;
    driversOnDuty: number;
  }> {
    try {
      const [trucks, drivers] = await Promise.all([
        this.getAllTrucks(),
        this.getAllDrivers()
      ]);

      return {
        totalTrucks: trucks.length,
        activeTrucks: trucks.filter(t => t.status === 'Active').length,
        totalDrivers: drivers.length,
        availableDrivers: drivers.filter(d => d.status === 'Available').length,
        trucksInMaintenance: trucks.filter(t => t.status === 'Maintenance').length,
        driversOnDuty: drivers.filter(d => d.status === 'On Duty').length
      };
    } catch (error) {
      console.error('Error getting fleet stats:', error);
      throw error;
    }
  }
}

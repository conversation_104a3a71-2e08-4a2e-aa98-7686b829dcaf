import { collection, getDocs, query, where, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Order } from '@/services/firebase/orders';
import { realTimeNotificationService } from './realTimeNotifications';

// Helper function to safely convert dates
function toDate(value: unknown): Date {
  if (value instanceof Date) return value;
  if (typeof value === 'string') return new Date(value);
  if (value && typeof value === 'object' && 'toDate' in value) {
    return (value as { toDate: () => Date }).toDate();
  }
  return new Date();
}

export interface WeeklyReportData {
  weekStart: Date;
  weekEnd: Date;
  totalOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
  pendingOrders: number;
  revenue: number;
  averageDeliveryTime: number;
  customerSatisfaction: number;
  topOrderTypes: Array<{ type: string; count: number }>;
  performanceMetrics: {
    deliveryRate: number;
    cancellationRate: number;
    onTimeDeliveryRate: number;
  };
}

class WeeklyReportService {
  private static instance: WeeklyReportService;

  private constructor() {}

  static getInstance(): WeeklyReportService {
    if (!WeeklyReportService.instance) {
      WeeklyReportService.instance = new WeeklyReportService();
    }
    return WeeklyReportService.instance;
  }

  /**
   * Generate weekly report for the current week
   */
  async generateCurrentWeekReport(): Promise<WeeklyReportData> {
    const now = new Date();
    const weekStart = this.getWeekStart(now);
    const weekEnd = this.getWeekEnd(weekStart);

    return this.generateWeeklyReport(weekStart, weekEnd);
  }

  /**
   * Generate weekly report for a specific date range
   */
  async generateWeeklyReport(weekStart: Date, weekEnd: Date): Promise<WeeklyReportData> {
    try {
      // Fetch orders for the week
      const orders = await this.getOrdersForWeek(weekStart, weekEnd);

      // Calculate metrics
      const totalOrders = orders.length;
      const deliveredOrders = orders.filter(order => order.status === 'Delivered').length;
      const cancelledOrders = orders.filter(order => order.status === 'Cancelled').length;
      const pendingOrders = orders.filter(order => 
        !['Delivered', 'Cancelled'].includes(order.status)
      ).length;

      // Calculate revenue (only from delivered orders)
      const revenue = orders
        .filter(order => order.status === 'Delivered')
        .reduce((sum, order) => sum + (order.totalAmount || 0), 0);

      // Calculate average delivery time
      const averageDeliveryTime = this.calculateAverageDeliveryTime(orders);

      // Calculate customer satisfaction (mock data for now)
      const customerSatisfaction = this.calculateCustomerSatisfaction(orders);

      // Get top order types
      const topOrderTypes = this.getTopOrderTypes(orders);

      // Calculate performance metrics
      const performanceMetrics = {
        deliveryRate: totalOrders > 0 ? (deliveredOrders / totalOrders) * 100 : 0,
        cancellationRate: totalOrders > 0 ? (cancelledOrders / totalOrders) * 100 : 0,
        onTimeDeliveryRate: this.calculateOnTimeDeliveryRate(orders)
      };

      return {
        weekStart,
        weekEnd,
        totalOrders,
        deliveredOrders,
        cancelledOrders,
        pendingOrders,
        revenue,
        averageDeliveryTime,
        customerSatisfaction,
        topOrderTypes,
        performanceMetrics
      };
    } catch (error) {
      console.error('Error generating weekly report:', error);
      throw error;
    }
  }

  /**
   * Send weekly report notification to users
   */
  async sendWeeklyReportNotification(userId: string): Promise<void> {
    try {
      const reportData = await this.generateCurrentWeekReport();
      
      await realTimeNotificationService.sendWeeklyReport(userId, {
        totalOrders: reportData.totalOrders,
        deliveredOrders: reportData.deliveredOrders,
        revenue: reportData.revenue,
        weekStart: reportData.weekStart,
        weekEnd: reportData.weekEnd
      });

      console.log(`✅ Weekly report notification sent to user: ${userId}`);
    } catch (error) {
      console.error('Error sending weekly report notification:', error);
    }
  }

  /**
   * Get orders for a specific week
   */
  private async getOrdersForWeek(weekStart: Date, weekEnd: Date): Promise<Order[]> {
    try {
      const ordersRef = collection(db, 'orders');
      const weekStartTimestamp = Timestamp.fromDate(weekStart);
      const weekEndTimestamp = Timestamp.fromDate(weekEnd);

      const q = query(
        ordersRef,
        where('createdAt', '>=', weekStartTimestamp),
        where('createdAt', '<=', weekEndTimestamp)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Order));
    } catch (error) {
      console.error('Error fetching orders for week:', error);
      return [];
    }
  }

  /**
   * Calculate average delivery time in hours
   */
  private calculateAverageDeliveryTime(orders: Order[]): number {
    const deliveredOrders = orders.filter(order =>
      order.status === 'Delivered' && order.createdAt && order.updatedAt
    );

    if (deliveredOrders.length === 0) return 0;

    const totalDeliveryTime = deliveredOrders.reduce((sum, order) => {
      const createdAt = toDate(order.createdAt);
      const deliveredAt = toDate(order.updatedAt);
      const deliveryTime = (deliveredAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60); // Convert to hours
      return sum + deliveryTime;
    }, 0);

    return Math.round(totalDeliveryTime / deliveredOrders.length * 10) / 10; // Round to 1 decimal place
  }

  /**
   * Calculate customer satisfaction (mock implementation)
   */
  private calculateCustomerSatisfaction(orders: Order[]): number {
    const deliveredOrders = orders.filter(order => order.status === 'Delivered');
    if (deliveredOrders.length === 0) return 0;

    // Mock calculation based on delivery performance
    // In a real implementation, this would be based on actual customer feedback
    const onTimeDeliveries = deliveredOrders.filter(order => {
      if (!order.estimatedDeliveryTime || !order.updatedAt) return true;
      const estimatedTime = toDate(order.estimatedDeliveryTime);
      const actualTime = toDate(order.updatedAt);
      return actualTime <= estimatedTime;
    }).length;

    const satisfactionScore = (onTimeDeliveries / deliveredOrders.length) * 100;
    return Math.round(satisfactionScore * 10) / 10;
  }

  /**
   * Get top order types for the week
   */
  private getTopOrderTypes(orders: Order[]): Array<{ type: string; count: number }> {
    const orderTypeCounts: Record<string, number> = {};

    orders.forEach(order => {
      const type = order.orderType || 'Unknown';
      orderTypeCounts[type] = (orderTypeCounts[type] || 0) + 1;
    });

    return Object.entries(orderTypeCounts)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // Top 5 order types
  }

  /**
   * Calculate on-time delivery rate
   */
  private calculateOnTimeDeliveryRate(orders: Order[]): number {
    const deliveredOrders = orders.filter(order => order.status === 'Delivered');
    if (deliveredOrders.length === 0) return 0;

    const onTimeDeliveries = deliveredOrders.filter(order => {
      if (!order.estimatedDeliveryTime || !order.updatedAt) return true;
      const estimatedTime = toDate(order.estimatedDeliveryTime);
      const actualTime = toDate(order.updatedAt);
      return actualTime <= estimatedTime;
    }).length;

    return Math.round((onTimeDeliveries / deliveredOrders.length) * 100 * 10) / 10;
  }

  /**
   * Get start of week (Monday)
   */
  private getWeekStart(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    const weekStart = new Date(d.setDate(diff));
    weekStart.setHours(0, 0, 0, 0);
    return weekStart;
  }

  /**
   * Get end of week (Sunday)
   */
  private getWeekEnd(weekStart: Date): Date {
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);
    return weekEnd;
  }

  /**
   * Schedule weekly report generation (to be called by a cron job or scheduler)
   */
  async scheduleWeeklyReports(): Promise<void> {
    try {
      // In a real implementation, you would get all active users
      // For now, we'll send to a default admin user
      const adminUserId = 'admin';
      
      await this.sendWeeklyReportNotification(adminUserId);
      console.log('✅ Weekly reports scheduled and sent');
    } catch (error) {
      console.error('Error scheduling weekly reports:', error);
    }
  }
}

// Export singleton instance
export const weeklyReportService = WeeklyReportService.getInstance();

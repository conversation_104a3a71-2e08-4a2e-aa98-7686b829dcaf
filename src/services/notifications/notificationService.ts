import { messaging } from '@/lib/firebase';
import { getToken, onMessage, isSupported } from 'firebase/messaging';
import { UserPreferencesService, UserSettings } from '@/services/firebase/userPreferences';
import { Order } from '@/services/firebase/orders';

export interface NotificationData {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: Record<string, unknown>;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
}

export interface SystemAlert {
  id: string;
  type: 'maintenance' | 'update' | 'warning' | 'info';
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  expiresAt?: Date;
}

export interface WeeklyReportData {
  weekStart: Date;
  weekEnd: Date;
  totalOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
  revenue: number;
  averageDeliveryTime: number;
  customerSatisfaction: number;
}

export type NotificationType = 
  | 'order_status_change'
  | 'order_delivered'
  | 'order_cancelled'
  | 'system_alert'
  | 'weekly_report'
  | 'general';

export class NotificationService {
  private static instance: NotificationService;
  private fcmToken: string | null = null;
  private isInitialized = false;

  private constructor() {}

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Check if messaging is supported
      const messagingSupported = await isSupported();

      // Request notification permission
      const permission = await this.requestPermission();

      if (permission === 'granted' && messaging && messagingSupported) {
        // Get FCM token
        this.fcmToken = await this.getFirebaseToken();

        // Set up message listener
        this.setupMessageListener();

        console.log('✅ Notification service initialized successfully');
      } else {
        console.log('📵 Notification permission denied or messaging not supported');
      }

      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error);
      this.isInitialized = true; // Mark as initialized even if failed to prevent retries
    }
  }

  /**
   * Request notification permission from the user
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return 'denied';
    }

    if (Notification.permission === 'granted') {
      return 'granted';
    }

    if (Notification.permission === 'denied') {
      return 'denied';
    }

    // Request permission
    const permission = await Notification.requestPermission();
    return permission;
  }

  /**
   * Get FCM token for push notifications
   */
  private async getFirebaseToken(): Promise<string | null> {
    if (!messaging) return null;

    try {
      const token = await getToken(messaging, {
        vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY
      });

      if (token) {
        console.log('📱 FCM Token obtained:', token);
        return token;
      } else {
        console.log('No registration token available');
        return null;
      }
    } catch (error) {
      console.error('An error occurred while retrieving token:', error);
      return null;
    }
  }

  /**
   * Set up listener for foreground messages
   */
  private setupMessageListener(): void {
    if (!messaging) return;

    onMessage(messaging, (payload) => {
      console.log('📨 Message received in foreground:', payload);
      
      // Show notification if the app is in foreground
      if (payload.notification) {
        this.showBrowserNotification({
          title: payload.notification.title || 'Zawaya Delivery',
          body: payload.notification.body || '',
          icon: payload.notification.icon || '/favicon.ico',
          data: payload.data
        });
      }
    });
  }

  /**
   * Show browser notification
   */
  private showBrowserNotification(data: NotificationData): void {
    if (Notification.permission !== 'granted') return;

    const notificationOptions: NotificationOptions = {
      body: data.body,
      icon: data.icon || '/favicon.ico',
      badge: data.badge || '/favicon.ico',
      data: data.data,
      requireInteraction: true,
      tag: 'zawaya-delivery'
    };

    const notification = new Notification(data.title, notificationOptions);

    // Handle notification click
    notification.onclick = () => {
      window.focus();
      notification.close();
      
      // Handle navigation based on notification data
      if (data.data?.url) {
        window.location.href = data.data.url as string;
      }
    };

    // Auto close after 10 seconds
    setTimeout(() => {
      notification.close();
    }, 10000);
  }

  /**
   * Send notification based on user preferences
   */
  async sendNotification(
    userId: string,
    type: NotificationType,
    data: NotificationData,
    emailData?: {
      recipientEmail: string;
      subject: string;
      htmlBody: string;
    }
  ): Promise<void> {
    try {
      // Get user preferences
      const preferences = await UserPreferencesService.getUserPreferences(userId);
      if (!preferences) {
        console.warn('No user preferences found for user:', userId);
        return;
      }

      const settings = preferences.settings;

      // Check if notifications are enabled for this type
      if (!this.shouldSendNotification(settings, type)) {
        console.log(`Notifications disabled for type: ${type}`);
        return;
      }

      // Send push notification if enabled
      if (settings.pushNotifications && this.fcmToken) {
        this.showBrowserNotification(data);
      }

      // Send email notification if enabled and email data provided
      if (settings.emailNotifications && emailData) {
        await this.sendEmailNotification(emailData);
      }

    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  /**
   * Check if notification should be sent based on user preferences
   */
  private shouldSendNotification(settings: UserSettings, type: NotificationType): boolean {
    switch (type) {
      case 'order_status_change':
      case 'order_delivered':
      case 'order_cancelled':
        return settings.orderUpdates;
      case 'system_alert':
        return settings.systemAlerts;
      case 'weekly_report':
        return settings.weeklyReports;
      default:
        return settings.pushNotifications || settings.emailNotifications;
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(emailData: {
    recipientEmail: string;
    subject: string;
    htmlBody: string;
  }): Promise<void> {
    try {
      // This would integrate with your email service
      // For now, we'll use a simple implementation
      console.log('📧 Sending email notification:', emailData.subject);
      
      // You can integrate with EmailService here for more complex emails
      // await EmailService.sendCustomEmail(emailData);
      
    } catch (error) {
      console.error('Error sending email notification:', error);
    }
  }

  /**
   * Send order notification (for internal use by real-time service)
   */
  async sendOrderNotification(
    userId: string,
    order: Order,
    type: 'delivered' | 'cancelled' | 'status_change'
  ): Promise<void> {
    const notificationData: NotificationData = {
      title: `Order ${order.orderId}`,
      body: this.getOrderNotificationBody(order, type),
      icon: '/favicon.ico',
      data: {
        orderId: order.id,
        orderNumber: order.orderId,
        type: 'order',
        status: order.status,
        url: `/orders/${order.id}`
      }
    };

    // Only send in-app and push notifications, email is handled separately
    await this.sendNotification(
      userId,
      type === 'delivered' ? 'order_delivered' : type === 'cancelled' ? 'order_cancelled' : 'order_status_change',
      notificationData
    );
  }

  /**
   * Get order notification body text
   */
  private getOrderNotificationBody(order: Order, type: 'delivered' | 'cancelled' | 'status_change'): string {
    switch (type) {
      case 'delivered':
        return `Order has been successfully delivered to ${order.customerName}`;
      case 'cancelled':
        return `Order has been cancelled. Reason: ${order.cancellationReason || 'Not specified'}`;
      case 'status_change':
        return `Order status updated to: ${order.status}`;
      default:
        return `Order ${order.orderId} has been updated`;
    }
  }

  /**
   * Get FCM token for the current user
   */
  getFCMToken(): string | null {
    return this.fcmToken;
  }

  /**
   * Check if notifications are supported and permission is granted
   */
  isNotificationSupported(): boolean {
    return 'Notification' in window && Notification.permission === 'granted';
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();

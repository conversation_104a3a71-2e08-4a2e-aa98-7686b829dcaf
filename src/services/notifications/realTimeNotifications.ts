import { collection, onSnapshot, query, orderBy, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Order } from '@/services/firebase/orders';
import { notificationService } from './notificationService';

export interface OrderNotificationData {
  orderId: string;
  customerName: string;
  status: string;
  previousStatus?: string;
  timestamp: Date;
  type: 'status_change' | 'new_order' | 'delivered' | 'cancelled';
}

class RealTimeNotificationService {
  private static instance: RealTimeNotificationService;
  private orderListener: (() => void) | null = null;
  private lastProcessedOrders: Map<string, Order> = new Map();
  private isInitialized = false;

  private constructor() {}

  static getInstance(): RealTimeNotificationService {
    if (!RealTimeNotificationService.instance) {
      RealTimeNotificationService.instance = new RealTimeNotificationService();
    }
    return RealTimeNotificationService.instance;
  }

  /**
   * Initialize real-time order monitoring
   */
  async initialize(userId: string): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Listen to order changes
      this.startOrderListener(userId);
      this.isInitialized = true;
      console.log('✅ Real-time notifications initialized');
    } catch (error) {
      console.error('❌ Failed to initialize real-time notifications:', error);
    }
  }

  /**
   * Start listening to order changes
   */
  private startOrderListener(userId: string): void {
    const ordersRef = collection(db, 'orders');
    const ordersQuery = query(
      ordersRef,
      orderBy('updatedAt', 'desc'),
      limit(50) // Monitor last 50 orders for changes
    );

    this.orderListener = onSnapshot(ordersQuery, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        const order = { id: change.doc.id, ...change.doc.data() } as Order;
        
        if (change.type === 'added') {
          this.handleNewOrder(userId, order);
        } else if (change.type === 'modified') {
          this.handleOrderUpdate(userId, order);
        }
        
        // Update our local cache
        this.lastProcessedOrders.set(order.id, order);
      });
    }, (error) => {
      console.error('Error listening to order changes:', error);
    });
  }

  /**
   * Handle new order creation
   */
  private async handleNewOrder(userId: string, order: Order): Promise<void> {
    // Skip if this order was already processed
    if (this.lastProcessedOrders.has(order.id)) return;

    try {
      await notificationService.sendNotification(
        userId,
        'order_status_change',
        {
          title: 'New Order Created',
          body: `Order ${order.orderId} has been created for ${order.customerName}`,
          icon: '/favicon.ico',
          data: {
            orderId: order.id,
            orderNumber: order.orderId,
            type: 'order',
            url: `/orders/${order.id}`
          }
        }
      );
    } catch (error) {
      console.error('Error sending new order notification:', error);
    }
  }

  /**
   * Handle order status updates
   */
  private async handleOrderUpdate(userId: string, order: Order): Promise<void> {
    const previousOrder = this.lastProcessedOrders.get(order.id);
    
    // Skip if no previous state or status hasn't changed
    if (!previousOrder || previousOrder.status === order.status) return;

    try {
      let notificationType: 'order_delivered' | 'order_cancelled' | 'order_status_change' = 'order_status_change';
      let title = 'Order Status Updated';
      let body = `Order ${order.orderId} status changed to ${order.status}`;

      // Determine notification type and customize message
      if (order.status === 'Delivered') {
        notificationType = 'order_delivered';
        title = 'Order Delivered';
        body = `Order ${order.orderId} has been successfully delivered to ${order.customerName}`;
      } else if (order.status === 'Cancelled') {
        notificationType = 'order_cancelled';
        title = 'Order Cancelled';
        body = `Order ${order.orderId} has been cancelled`;
        if (order.cancellationReason) {
          body += `. Reason: ${order.cancellationReason}`;
        }
      } else {
        // For other status changes, provide more context
        const statusMessages: Record<string, string> = {
          'Started': 'has been started and is being processed',
          'Moved to Supplier': 'has been moved to the supplier',
          'Arrived at Supplier': 'has arrived at the supplier',
          'Moving to Customer': 'is now being delivered to the customer',
          'Arrived at Customer': 'has arrived at the customer location'
        };
        
        const statusMessage = statusMessages[order.status] || `status changed to ${order.status}`;
        body = `Order ${order.orderId} ${statusMessage}`;
      }

      await notificationService.sendNotification(
        userId,
        notificationType,
        {
          title,
          body,
          icon: '/favicon.ico',
          data: {
            orderId: order.id,
            orderNumber: order.orderId,
            type: 'order',
            status: order.status,
            previousStatus: previousOrder.status,
            url: `/orders/${order.id}`
          }
        }
      );
    } catch (error) {
      console.error('Error sending order update notification:', error);
    }
  }

  /**
   * Send system alert notification
   */
  async sendSystemAlert(
    userId: string,
    title: string,
    message: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<void> {
    try {
      await notificationService.sendNotification(
        userId,
        'system_alert',
        {
          title,
          body: message,
          icon: '/favicon.ico',
          data: {
            type: 'system_alert',
            severity,
            url: '/dashboard'
          }
        }
      );
    } catch (error) {
      console.error('Error sending system alert:', error);
    }
  }

  /**
   * Send weekly report notification
   */
  async sendWeeklyReport(
    userId: string,
    reportData: {
      totalOrders: number;
      deliveredOrders: number;
      revenue: number;
      weekStart: Date;
      weekEnd: Date;
    }
  ): Promise<void> {
    try {
      const deliveryRate = reportData.totalOrders > 0 
        ? Math.round((reportData.deliveredOrders / reportData.totalOrders) * 100)
        : 0;

      await notificationService.sendNotification(
        userId,
        'weekly_report',
        {
          title: 'Weekly Report Available',
          body: `${reportData.totalOrders} orders, ${deliveryRate}% delivery rate, ${reportData.revenue.toLocaleString()} EGP revenue`,
          icon: '/favicon.ico',
          data: {
            type: 'weekly_report',
            reportData,
            url: '/reports'
          }
        }
      );
    } catch (error) {
      console.error('Error sending weekly report notification:', error);
    }
  }

  /**
   * Stop listening to order changes
   */
  cleanup(): void {
    if (this.orderListener) {
      this.orderListener();
      this.orderListener = null;
    }
    this.lastProcessedOrders.clear();
    this.isInitialized = false;
  }

  /**
   * Check if service is initialized
   */
  isServiceInitialized(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const realTimeNotificationService = RealTimeNotificationService.getInstance();

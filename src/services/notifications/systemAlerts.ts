import { collection, addDoc, getDocs, query, where, orderBy, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { realTimeNotificationService } from './realTimeNotifications';

export interface SystemAlert {
  id?: string;
  type: 'maintenance' | 'update' | 'warning' | 'info';
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  targetUsers: string[]; // User IDs to notify, empty array means all users
  isActive: boolean;
  scheduledAt?: Date;
  expiresAt?: Date;
  createdAt: Timestamp;
  createdBy: string;
}

class SystemAlertService {
  private static instance: SystemAlertService;
  private readonly ALERTS_COLLECTION = 'systemAlerts';

  private constructor() {}

  static getInstance(): SystemAlertService {
    if (!SystemAlertService.instance) {
      SystemAlertService.instance = new SystemAlertService();
    }
    return SystemAlertService.instance;
  }

  /**
   * Create and send a system alert
   */
  async createAlert(
    alertData: Omit<SystemAlert, 'id' | 'createdAt'>,
    sendImmediately: boolean = true
  ): Promise<string> {
    try {
      const alert: Omit<SystemAlert, 'id'> = {
        ...alertData,
        createdAt: Timestamp.now()
      };

      // Save to Firestore
      const alertsRef = collection(db, this.ALERTS_COLLECTION);
      const docRef = await addDoc(alertsRef, alert);

      // Send notifications immediately if requested
      if (sendImmediately && alert.isActive) {
        await this.sendAlertNotifications(alert);
      }

      console.log(`✅ System alert created: ${alert.title}`);
      return docRef.id;
    } catch (error) {
      console.error('Error creating system alert:', error);
      throw error;
    }
  }

  /**
   * Send alert notifications to users
   */
  private async sendAlertNotifications(alert: SystemAlert): Promise<void> {
    try {
      // If targetUsers is empty, send to all active users
      // For now, we'll send to a default admin user
      // In a real implementation, you'd get all active users from your user management system
      const targetUsers = alert.targetUsers.length > 0 
        ? alert.targetUsers 
        : ['admin']; // Default admin user

      for (const userId of targetUsers) {
        await realTimeNotificationService.sendSystemAlert(
          userId,
          alert.title,
          alert.message,
          alert.severity
        );
      }
    } catch (error) {
      console.error('Error sending alert notifications:', error);
    }
  }

  /**
   * Get active system alerts
   */
  async getActiveAlerts(): Promise<SystemAlert[]> {
    try {
      const alertsRef = collection(db, this.ALERTS_COLLECTION);
      const q = query(
        alertsRef,
        where('isActive', '==', true),
        orderBy('createdAt', 'desc')
      );
      
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as SystemAlert));
    } catch (error) {
      console.error('Error fetching active alerts:', error);
      return [];
    }
  }

  /**
   * Send maintenance notification
   */
  async sendMaintenanceAlert(
    title: string,
    message: string,
    scheduledTime: Date,
    duration: string,
    createdBy: string
  ): Promise<void> {
    await this.createAlert({
      type: 'maintenance',
      title,
      message: `${message}. Scheduled for ${scheduledTime.toLocaleString()}. Expected duration: ${duration}`,
      severity: 'medium',
      targetUsers: [], // Send to all users
      isActive: true,
      scheduledAt: scheduledTime,
      createdBy
    });
  }

  /**
   * Send system update notification
   */
  async sendUpdateAlert(
    version: string,
    features: string[],
    createdBy: string
  ): Promise<void> {
    const message = `System updated to version ${version}. New features: ${features.join(', ')}`;
    
    await this.createAlert({
      type: 'update',
      title: 'System Update Available',
      message,
      severity: 'low',
      targetUsers: [],
      isActive: true,
      createdBy
    });
  }

  /**
   * Send critical warning
   */
  async sendCriticalWarning(
    title: string,
    message: string,
    createdBy: string
  ): Promise<void> {
    await this.createAlert({
      type: 'warning',
      title,
      message,
      severity: 'critical',
      targetUsers: [],
      isActive: true,
      createdBy
    });
  }

  /**
   * Send informational alert
   */
  async sendInfoAlert(
    title: string,
    message: string,
    targetUsers: string[] = [],
    createdBy: string
  ): Promise<void> {
    await this.createAlert({
      type: 'info',
      title,
      message,
      severity: 'low',
      targetUsers,
      isActive: true,
      createdBy
    });
  }

  /**
   * Predefined system alerts for common scenarios
   */
  async sendCommonAlerts() {
    const adminUser = 'admin'; // This should be replaced with actual admin user ID

    // Example maintenance alert
    const maintenanceTime = new Date();
    maintenanceTime.setHours(maintenanceTime.getHours() + 24); // Tomorrow
    
    await this.sendMaintenanceAlert(
      'Scheduled Maintenance',
      'System maintenance will be performed to improve performance and security',
      maintenanceTime,
      '2 hours',
      adminUser
    );
  }
}

// Export singleton instance
export const systemAlertService = SystemAlertService.getInstance();

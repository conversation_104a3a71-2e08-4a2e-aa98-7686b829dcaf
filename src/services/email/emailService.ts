import nodemailer from 'nodemailer';
import { Order } from '@/services/firebase/orders';

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

export interface EmailNotificationData {
  order: Order;
  recipientEmail: string;
  type: 'delivered' | 'cancelled';
}

export class EmailService {
  private static transporter: nodemailer.Transporter | null = null;

  private static getTransporter(): nodemailer.Transporter {
    if (!this.transporter) {
      // Use environment variables for email configuration
      const config: EmailConfig = {
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.EMAIL_PORT || '587'),
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
          user: process.env.EMAIL_USER || '',
          pass: process.env.EMAIL_PASS || ''
        }
      };

      this.transporter = nodemailer.createTransport(config);
    }

    return this.transporter;
  }

  static async sendOrderNotification(data: EmailNotificationData): Promise<boolean> {
    const { order, recipientEmail, type } = data;

    try {
      const transporter = this.getTransporter();

      // Skip sending if email credentials are not configured
      if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
        console.log('Email credentials not configured, skipping email notification');
        return false;
      }

      const subject = type === 'delivered'
        ? `✅ Order ${order.orderId} Successfully Delivered - Zawaya Stores`
        : `❌ Order ${order.orderId} Cancelled - Zawaya Stores`;

      const htmlBody = type === 'delivered'
        ? this.generateDeliveredEmailTemplate(order)
        : this.generateCancelledEmailTemplate(order);

      const mailOptions = {
        from: `"Zawaya Stores" <${process.env.EMAIL_USER}>`,
        to: recipientEmail,
        subject: subject,
        html: htmlBody
      };

      const result = await transporter.sendMail(mailOptions);
      console.log(`✅ Email sent successfully for order ${order.orderId}:`, result.messageId);
      return true;
    } catch (error) {
      console.error(`❌ Failed to send email for order ${order.orderId}:`, error);
      return false;
    }
  }

  private static generateDeliveredEmailTemplate(order: Order): string {
    const formattedDate = new Date().toLocaleString('en-EG', {
      timeZone: 'Africa/Cairo',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const formattedEstDeliveryTime = order.estimatedDeliveryTime 
      ? new Date(order.estimatedDeliveryTime).toLocaleString('en-EG', {
          timeZone: 'Africa/Cairo',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      : 'Not specified';

    const totalAmount = `${order.totalAmount.toLocaleString()} EGP`;
    const depositAmount = `${order.depositAmount.toLocaleString()} EGP`;

    // Calculate delivery performance
    const now = new Date();
    const estimatedTime = order.estimatedDeliveryTime ? new Date(order.estimatedDeliveryTime) : null;
    const deliveryStatus = estimatedTime 
      ? (now <= estimatedTime ? 'On Time ✅' : 'Late ⚠️')
      : 'No Estimate';

    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Delivered - Zawaya Stores</title>
  <style>
    body { font-family: 'Segoe UI', Arial, sans-serif; background-color: #f0f0f0; padding: 20px; color: #333; margin: 0; }
    .container { max-width: 600px; margin: 0 auto; background-color: #fff; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden; }
    .header { background: linear-gradient(45deg, #ff6200, #ff8c00); color: white; padding: 25px; text-align: center; }
    .header h1 { margin: 0; font-size: 26px; font-weight: 600; letter-spacing: 0.5px; }
    .content { padding: 30px; }
    .order-info { border-collapse: collapse; width: 100%; margin-bottom: 15px; }
    .order-info td { padding: 12px; border-bottom: 1px solid #e0e0e0; }
    .order-info .label { font-weight: bold; color: #424242; width: 40%; }
    .order-info .value { color: #616161; font-size: 15px; }
    .summary-box { background-color: #fafafa; border-radius: 8px; padding: 15px; margin-top: 20px; border-left: 4px solid #ff8c00; }
    .footer { text-align: center; padding: 15px; font-size: 13px; color: #757575; background-color: #f5f5f5; border-top: 1px solid #e0e0e0; }
    .badge { display: inline-block; padding: 5px 10px; border-radius: 15px; font-size: 13px; font-weight: bold; background-color: #ff8c00; color: white; }
    .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; letter-spacing: 1px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">ZAWAYA STORES</div>
      <h1>🎉 Order Successfully Delivered! 🚚</h1>
    </div>
    <div class="content">
      <div style="text-align: center; margin-bottom: 20px;">
        <span class="badge">Delivered</span>
        <p style="margin-top: 10px; color: #757575;">Order completed on ${formattedDate}</p>
      </div>

      <h2 style="border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; color: #424242;">📦 Order Details</h2>
      <table class="order-info">
        <tr>
          <td class="label">📋 Order ID:</td>
          <td class="value"><strong>${order.orderId}</strong></td>
        </tr>
        <tr>
          <td class="label">🔖 Order Type:</td>
          <td class="value">${order.orderType}</td>
        </tr>
        <tr>
          <td class="label">💰 Total Amount:</td>
          <td class="value"><strong>${totalAmount}</strong></td>
        </tr>
        <tr>
          <td class="label">💵 Deposit Amount:</td>
          <td class="value">${depositAmount}</td>
        </tr>
      </table>

      <h2 style="border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; margin-top: 25px; color: #424242;">👤 Customer Information</h2>
      <table class="order-info">
        <tr>
          <td class="label">👤 Name:</td>
          <td class="value"><strong>${order.customerName}</strong></td>
        </tr>
        <tr>
          <td class="label">🏠 Address:</td>
          <td class="value">${order.customerAddress}</td>
        </tr>
        <tr>
          <td class="label">📞 Phone:</td>
          <td class="value">${order.customerPhone}</td>
        </tr>
      </table>

      <h2 style="border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; margin-top: 25px; color: #424242;">🚚 Delivery Information</h2>
      <table class="order-info">
        <tr>
          <td class="label">🚚 Delivery Agent:</td>
          <td class="value"><strong>${order.deliveryAgent || 'Not assigned'}</strong></td>
        </tr>
        <tr>
          <td class="label">👨‍💼 Sales Rep:</td>
          <td class="value">${order.salesRep}</td>
        </tr>
        <tr>
          <td class="label">⏱️ Est. Delivery Time:</td>
          <td class="value">${formattedEstDeliveryTime}</td>
        </tr>
        <tr>
          <td class="label">📊 Delivery Status:</td>
          <td class="value">${deliveryStatus}</td>
        </tr>
      </table>

      <div class="summary-box">
        <h3 style="margin-top: 0; color: #424242;">💰 Financial Summary</h3>
        <p>Total Order Amount: <strong>${totalAmount}</strong></p>
        <p>Deposit Amount: <strong>${depositAmount}</strong></p>
        <p style="font-style: italic; color: #757575;">Please verify payment completion and reconcile any outstanding amounts.</p>
      </div>

      <div class="summary-box" style="margin-top: 15px;">
        <h3 style="margin-top: 0; color: #424242;">⏱️ Delivery Performance</h3>
        <p>Estimated Delivery Time: <strong>${formattedEstDeliveryTime}</strong></p>
        <p>Actual Delivery Time: <strong>${formattedDate}</strong></p>
        <p>Status: <strong>${deliveryStatus}</strong></p>
        <p style="font-style: italic; color: #757575;">Review delivery efficiency for this order.</p>
      </div>
    </div>
    <div class="footer">
      <p>This is an automated notification from Zawaya Stores Order System</p>
      <p>© ${new Date().getFullYear()} Zawaya Stores. All rights reserved.</p>
    </div>
  </div>
</body>
</html>`;
  }

  private static generateCancelledEmailTemplate(order: Order): string {
    const formattedDate = new Date().toLocaleString('en-EG', {
      timeZone: 'Africa/Cairo',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const totalAmount = `${order.totalAmount.toLocaleString()} EGP`;
    const depositAmount = `${order.depositAmount.toLocaleString()} EGP`;

    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Cancelled - Zawaya Stores</title>
  <style>
    body { font-family: 'Segoe UI', Arial, sans-serif; background-color: #f0f0f0; padding: 20px; color: #333; margin: 0; }
    .container { max-width: 600px; margin: 0 auto; background-color: #fff; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden; }
    .header { background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; padding: 25px; text-align: center; }
    .header h1 { margin: 0; font-size: 26px; font-weight: 600; letter-spacing: 0.5px; }
    .content { padding: 30px; }
    .order-info { border-collapse: collapse; width: 100%; margin-bottom: 15px; }
    .order-info td { padding: 12px; border-bottom: 1px solid #e0e0e0; }
    .order-info .label { font-weight: bold; color: #424242; width: 40%; }
    .order-info .value { color: #616161; font-size: 15px; }
    .summary-box { background-color: #fafafa; border-radius: 8px; padding: 15px; margin-top: 20px; border-left: 4px solid #e74c3c; }
    .footer { text-align: center; padding: 15px; font-size: 13px; color: #757575; background-color: #f5f5f5; border-top: 1px solid #e0e0e0; }
    .badge { display: inline-block; padding: 5px 10px; border-radius: 15px; font-size: 13px; font-weight: bold; background-color: #e74c3c; color: white; }
    .logo { font-size: 28px; font-weight: bold; margin-bottom: 10px; letter-spacing: 1px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">ZAWAYA STORES</div>
      <h1>❌ Order Cancelled</h1>
    </div>
    <div class="content">
      <div style="text-align: center; margin-bottom: 20px;">
        <span class="badge">Cancelled</span>
        <p style="margin-top: 10px; color: #757575;">Order cancelled on ${formattedDate}</p>
      </div>

      <h2 style="border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; color: #424242;">📦 Order Details</h2>
      <table class="order-info">
        <tr>
          <td class="label">📋 Order ID:</td>
          <td class="value"><strong>${order.orderId}</strong></td>
        </tr>
        <tr>
          <td class="label">🔖 Order Type:</td>
          <td class="value">${order.orderType}</td>
        </tr>
        <tr>
          <td class="label">💰 Total Amount:</td>
          <td class="value"><strong>${totalAmount}</strong></td>
        </tr>
        <tr>
          <td class="label">💵 Deposit Amount:</td>
          <td class="value">${depositAmount}</td>
        </tr>
        ${order.cancellationReason ? `
        <tr>
          <td class="label">📝 Cancellation Reason:</td>
          <td class="value"><strong>${order.cancellationReason}</strong></td>
        </tr>
        ` : ''}
      </table>

      <h2 style="border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; margin-top: 25px; color: #424242;">👤 Customer Information</h2>
      <table class="order-info">
        <tr>
          <td class="label">👤 Name:</td>
          <td class="value"><strong>${order.customerName}</strong></td>
        </tr>
        <tr>
          <td class="label">🏠 Address:</td>
          <td class="value">${order.customerAddress}</td>
        </tr>
        <tr>
          <td class="label">📞 Phone:</td>
          <td class="value">${order.customerPhone}</td>
        </tr>
      </table>

      <div class="summary-box">
        <h3 style="margin-top: 0; color: #424242;">💰 Financial Impact</h3>
        <p>Total Order Amount: <strong>${totalAmount}</strong></p>
        <p>Deposit Amount: <strong>${depositAmount}</strong></p>
        <p style="font-style: italic; color: #757575;">Please process any necessary refunds and update financial records accordingly.</p>
      </div>

      <div class="summary-box" style="margin-top: 15px;">
        <h3 style="margin-top: 0; color: #424242;">📋 Next Steps</h3>
        <ul style="margin: 10px 0; padding-left: 20px;">
          <li>Process customer refund if applicable</li>
          <li>Update inventory records</li>
          <li>Contact customer for feedback</li>
          <li>Review cancellation reason for process improvement</li>
        </ul>
      </div>
    </div>
    <div class="footer">
      <p>This is an automated notification from Zawaya Stores Order System</p>
      <p>© ${new Date().getFullYear()} Zawaya Stores. All rights reserved.</p>
    </div>
  </div>
</body>
</html>`;
  }
}

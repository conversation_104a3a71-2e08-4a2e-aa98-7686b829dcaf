import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { prepareForFirebase } from '@/utils/firebase';

// Report Configuration Types
export type DataSource = 'orders' | 'customers' | 'deliveries' | 'team' | 'fleet';
export type ChartType = 'bar' | 'line' | 'pie' | 'table' | 'metric';
export type TimePeriod = '7d' | '30d' | '3m' | '6m' | '1y' | 'all';

export interface ReportConfig {
  id: string;
  name: string;
  dataSource: DataSource;
  chartType: ChartType;
  period: TimePeriod;
  filters: Record<string, unknown>;
  groupBy?: string;
  aggregation?: 'count' | 'sum' | 'avg' | 'min' | 'max';
  field?: string;
  createdAt: Timestamp;
  lastRun?: Timestamp;
  userId: string;
}

export interface GeneratedReport {
  id: string;
  config: ReportConfig;
  data: Array<Record<string, unknown>>;
  summary: {
    totalRecords: number;
    totalValue?: number;
    averageValue?: number;
  };
  generatedAt: Timestamp;
  userId: string;
}

class ReportsService {
  private reportsCollection = 'reports';
  private generatedReportsCollection = 'generatedReports';

  // Report Configurations
  async createReportConfig(config: Omit<ReportConfig, 'id' | 'createdAt'>): Promise<string> {
    try {
      const cleanConfig = prepareForFirebase({
        ...config,
        createdAt: Timestamp.now()
      });

      const docRef = await addDoc(collection(db, this.reportsCollection), cleanConfig);
      return docRef.id;
    } catch (error) {
      console.error('Error creating report config:', error);
      throw error;
    }
  }

  async updateReportConfig(id: string, updates: Partial<ReportConfig>): Promise<void> {
    try {
      const cleanUpdates = prepareForFirebase({
        ...updates,
        lastRun: updates.lastRun || Timestamp.now()
      });

      const docRef = doc(db, this.reportsCollection, id);
      await updateDoc(docRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating report config:', error);
      throw error;
    }
  }

  async deleteReportConfig(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, this.reportsCollection, id));
    } catch (error) {
      console.error('Error deleting report config:', error);
      throw error;
    }
  }

  async getReportConfig(id: string): Promise<ReportConfig | null> {
    try {
      const docRef = doc(db, this.reportsCollection, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as ReportConfig;
      }
      return null;
    } catch (error) {
      console.error('Error getting report config:', error);
      throw error;
    }
  }

  async getAllReportConfigs(userId: string): Promise<ReportConfig[]> {
    try {
      const q = query(
        collection(db, this.reportsCollection),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ReportConfig[];
    } catch (error) {
      console.error('Error getting report configs:', error);
      throw error;
    }
  }

  // Generated Reports
  async saveGeneratedReport(report: Omit<GeneratedReport, 'id' | 'generatedAt'>): Promise<string> {
    try {
      const cleanReport = prepareForFirebase({
        ...report,
        generatedAt: Timestamp.now()
      });

      const docRef = await addDoc(collection(db, this.generatedReportsCollection), cleanReport);
      return docRef.id;
    } catch (error) {
      console.error('Error saving generated report:', error);
      throw error;
    }
  }

  async getGeneratedReport(id: string): Promise<GeneratedReport | null> {
    try {
      const docRef = doc(db, this.generatedReportsCollection, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as GeneratedReport;
      }
      return null;
    } catch (error) {
      console.error('Error getting generated report:', error);
      throw error;
    }
  }

  async getUserGeneratedReports(userId: string, limit?: number): Promise<GeneratedReport[]> {
    try {
      let q = query(
        collection(db, this.generatedReportsCollection),
        where('userId', '==', userId),
        orderBy('generatedAt', 'desc')
      );

      if (limit) {
        q = query(q, orderBy('generatedAt', 'desc'));
      }
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as GeneratedReport[];
    } catch (error) {
      console.error('Error getting user generated reports:', error);
      throw error;
    }
  }

  async deleteGeneratedReport(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, this.generatedReportsCollection, id));
    } catch (error) {
      console.error('Error deleting generated report:', error);
      throw error;
    }
  }

  // Report Generation Helpers
  async generateReportFromConfig(config: ReportConfig): Promise<GeneratedReport> {
    try {
      // This would integrate with the API route we created
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error('Failed to generate report');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to generate report');
      }

      // Save the generated report to Firestore
      const reportId = await this.saveGeneratedReport({
        config,
        data: result.data.data,
        summary: result.data.summary,
        userId: config.userId
      });

      // Update the config with last run time
      await this.updateReportConfig(config.id, {
        lastRun: Timestamp.now()
      });

      return {
        id: reportId,
        config,
        data: result.data.data,
        summary: result.data.summary,
        generatedAt: Timestamp.now(),
        userId: config.userId
      };
    } catch (error) {
      console.error('Error generating report from config:', error);
      throw error;
    }
  }

  // Search and Filter
  async searchReportConfigs(userId: string, searchTerm: string): Promise<ReportConfig[]> {
    try {
      const allConfigs = await this.getAllReportConfigs(userId);
      
      if (!searchTerm.trim()) {
        return allConfigs;
      }

      const term = searchTerm.toLowerCase();
      return allConfigs.filter(config => 
        config.name.toLowerCase().includes(term) ||
        config.dataSource.toLowerCase().includes(term) ||
        config.chartType.toLowerCase().includes(term)
      );
    } catch (error) {
      console.error('Error searching report configs:', error);
      throw error;
    }
  }

  async getReportConfigsByDataSource(userId: string, dataSource: DataSource): Promise<ReportConfig[]> {
    try {
      const q = query(
        collection(db, this.reportsCollection),
        where('userId', '==', userId),
        where('dataSource', '==', dataSource),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ReportConfig[];
    } catch (error) {
      console.error('Error getting reports by data source:', error);
      throw error;
    }
  }

  async getReportConfigsByChartType(userId: string, chartType: ChartType): Promise<ReportConfig[]> {
    try {
      const q = query(
        collection(db, this.reportsCollection),
        where('userId', '==', userId),
        where('chartType', '==', chartType),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ReportConfig[];
    } catch (error) {
      console.error('Error getting reports by chart type:', error);
      throw error;
    }
  }
}

const reportsService = new ReportsService();
export default reportsService;

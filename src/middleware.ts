import createMiddleware from 'next-intl/middleware';
import { locales, defaultLocale } from './i18n/config';

export default createMiddleware({
  // A list of all locales that are supported
  locales,

  // Used when no locale matches
  defaultLocale,

  // Always add locale prefix for consistency
  localePrefix: 'always',

  // Preserve locale from URL and cookies
  localeDetection: true,

  // Use the locale from the URL path as the primary source
  // This prevents automatic switching to default locale
  pathnames: {
    '/': '/',
    '/dashboard': '/dashboard',
    '/orders': '/orders',
    '/online-orders': '/online-orders',
    '/customers': '/customers',
    '/team': '/team',
    '/fleet': '/fleet',
    '/reports': '/reports',
    '/login': '/login',
    '/register': '/register'
  }
});

export const config = {
  // Match only internationalized pathnames
  matcher: [
    // Enable a redirect to a matching locale at the root
    '/',

    // Set a cookie to remember the previous locale for
    // all requests that have a locale prefix
    '/(ar|en)/:path*',

    // Enable redirects that add missing locales
    // (e.g. `/pathnames` -> `/en/pathnames`)
    '/((?!_next|_vercel|.*\\.|api|favicon.ico).*)'
  ]
};

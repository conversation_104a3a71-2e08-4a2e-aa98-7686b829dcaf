// Firebase Messaging Service Worker
// This file handles background push notifications

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/11.8.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.8.1/firebase-messaging-compat.js');

// Initialize Firebase in the service worker
firebase.initializeApp({
  apiKey: "AIzaSyDGZ5lIgPxC1c2FksxIa8Ak7ngehw-G3n8",
  authDomain: "web-delivery-61cae.firebaseapp.com",
  projectId: "web-delivery-61cae",
  storageBucket: "web-delivery-61cae.firebasestorage.app",
  messagingSenderId: "158352075738",
  appId: "1:158352075738:web:c83d5ef788fcd6865b6ee3",
  measurementId: "G-9QFNF53DJY"
});

// Initialize Firebase Messaging
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  // Customize notification here
  const notificationTitle = payload.notification?.title || 'Zawaya Delivery';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: payload.notification?.icon || '/favicon.ico',
    badge: '/favicon.ico',
    data: payload.data || {},
    actions: [
      {
        action: 'open',
        title: 'Open App',
        icon: '/favicon.ico'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ],
    requireInteraction: true,
    tag: 'zawaya-delivery',
    timestamp: Date.now(),
    vibrate: [200, 100, 200],
    silent: false
  };

  // Show the notification
  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click events
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received:', event);

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Handle notification click
  const urlToOpen = event.notification.data?.url || '/dashboard';
  
  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no window/tab is open, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Handle notification close events
self.addEventListener('notificationclose', (event) => {
  console.log('[firebase-messaging-sw.js] Notification closed:', event);
  
  // Track notification dismissal if needed
  // You can send analytics data here
});

// Handle push events (for additional customization)
self.addEventListener('push', (event) => {
  console.log('[firebase-messaging-sw.js] Push event received:', event);
  
  // This is handled by Firebase Messaging, but you can add custom logic here
});

// Handle service worker installation
self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker installing...');
  self.skipWaiting();
});

// Handle service worker activation
self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker activating...');
  event.waitUntil(self.clients.claim());
});

// Handle fetch events (optional - for caching strategies)
self.addEventListener('fetch', (event) => {
  // You can add caching strategies here if needed
  // For now, we'll just let the browser handle fetch requests normally
});

// Utility function to get notification data
function getNotificationData(payload) {
  return {
    title: payload.notification?.title || 'Zawaya Delivery',
    body: payload.notification?.body || 'You have a new notification',
    icon: payload.notification?.icon || '/favicon.ico',
    badge: '/favicon.ico',
    data: payload.data || {},
    tag: 'zawaya-delivery',
    requireInteraction: true,
    actions: [
      {
        action: 'open',
        title: 'Open',
        icon: '/favicon.ico'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ]
  };
}

// Utility function to handle different notification types
function handleNotificationType(data) {
  const type = data.type;
  
  switch (type) {
    case 'order':
      return {
        url: `/orders/${data.orderId}`,
        actions: [
          { action: 'view_order', title: 'View Order' },
          { action: 'dismiss', title: 'Dismiss' }
        ]
      };
    case 'system_alert':
      return {
        url: '/dashboard',
        actions: [
          { action: 'view_alert', title: 'View Alert' },
          { action: 'dismiss', title: 'Dismiss' }
        ]
      };
    case 'weekly_report':
      return {
        url: '/reports',
        actions: [
          { action: 'view_report', title: 'View Report' },
          { action: 'dismiss', title: 'Dismiss' }
        ]
      };
    default:
      return {
        url: '/dashboard',
        actions: [
          { action: 'open', title: 'Open App' },
          { action: 'dismiss', title: 'Dismiss' }
        ]
      };
  }
}

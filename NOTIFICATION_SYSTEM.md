# Notification System Documentation

## Overview

The Zawaya Delivery application now includes a comprehensive notification system that provides real-time updates to users through multiple channels:

- **Push Notifications**: Browser-based push notifications using Firebase Cloud Messaging (FCM)
- **Email Notifications**: Email alerts for important events
- **In-App Notifications**: Real-time notifications within the application interface
- **User Preferences**: Granular control over notification types and delivery methods

## Features

### 1. Notification Types

- **Order Updates**: Status changes, deliveries, cancellations
- **System Alerts**: Maintenance notifications, system updates
- **Weekly Reports**: Performance summaries and analytics
- **Email Notifications**: Order confirmations and status updates
- **Push Notifications**: Real-time browser notifications

### 2. User Preferences

Users can control their notification preferences through the Settings modal:

- ✅ **Email Notifications**: Receive notifications via email
- ✅ **Push Notifications**: Receive push notifications in browser
- ✅ **Order Updates**: Get notified about order status changes
- ✅ **System Alerts**: Receive system maintenance and alerts
- ✅ **Weekly Reports**: Get weekly performance reports

### 3. Notification Bell

The header includes a notification bell that shows:
- Unread notification count
- Dropdown with recent notifications
- Mark as read/unread functionality
- Clear individual or all notifications
- Permission request prompts

## Implementation

### Core Components

1. **NotificationService** (`src/services/notifications/notificationService.ts`)
   - Centralized notification management
   - Firebase Cloud Messaging integration
   - User preference checking
   - Email notification coordination

2. **NotificationContext** (`src/contexts/NotificationContext.tsx`)
   - React context for notification state
   - Automatic initialization
   - Permission management

3. **NotificationBell** (`src/components/NotificationBell.tsx`)
   - Header notification interface
   - Dropdown with notification list
   - Real-time updates

4. **useNotifications Hook** (`src/hooks/useNotifications.ts`)
   - React hook for notification functionality
   - State management
   - Action handlers

### Service Worker

The application includes a service worker (`public/firebase-messaging-sw.js`) that handles:
- Background push notifications
- Notification click handling
- Custom notification actions
- Offline notification queuing

## Setup Instructions

### 1. Firebase Cloud Messaging Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `web-delivery-61cae`
3. Navigate to Project Settings > Cloud Messaging
4. Generate a Web Push certificate (VAPID key)
5. Add the VAPID key to your environment variables:

```env
NEXT_PUBLIC_FIREBASE_VAPID_KEY=your-vapid-key-here
```

### 2. Environment Configuration

Copy `.env.example` to `.env.local` and configure:

```env
# Firebase Cloud Messaging
NEXT_PUBLIC_FIREBASE_VAPID_KEY=your-vapid-key-here

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### 3. Browser Permissions

Users need to grant notification permissions:
1. The app will automatically prompt for permissions
2. Users can manually enable in Settings > Notifications
3. Browser settings can override application permissions

## Usage

### Testing Notifications

In development mode, a test panel is available on the dashboard with buttons to test different notification types:

- Order Delivered
- Order Cancelled
- Order Status Change
- System Alert
- Weekly Report

### Sending Notifications Programmatically

```typescript
import { notificationService } from '@/services/notifications/notificationService';

// Send order notification
await notificationService.sendOrderNotification(
  userId,
  order,
  'delivered' // or 'cancelled' or 'status_change'
);

// Send custom notification
await notificationService.sendNotification(
  userId,
  'system_alert',
  {
    title: 'System Maintenance',
    body: 'Scheduled maintenance will begin at 2:00 AM',
    icon: '/favicon.ico',
    data: { url: '/dashboard' }
  }
);
```

### Integration with Order Updates

The notification system is automatically integrated with order status changes in `/api/orders/route.ts`. When an order status changes:

1. The system checks user notification preferences
2. Sends appropriate notifications (push, email, in-app)
3. Logs notification attempts
4. Handles failures gracefully

## User Experience

### Settings Interface

The notification preferences are accessible through:
1. User menu > Settings
2. Notifications tab
3. Toggle switches for each notification type
4. Real-time permission status display
5. One-click permission requests

### Notification Bell

The notification bell in the header provides:
- Visual indicator of unread notifications
- Dropdown with recent notifications
- Quick actions (mark as read, clear)
- Navigation to relevant pages
- Permission management

### Browser Notifications

When enabled, users receive:
- Desktop notifications even when the app is not active
- Sound alerts (configurable by browser)
- Action buttons for quick responses
- Automatic notification grouping

## Technical Details

### Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Action   │───▶│ Notification     │───▶│ Multiple        │
│ (Order Update)  │    │ Service          │    │ Channels        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
                    ┌──────────────────┐    ┌─────────────────┐
                    │ User Preferences │    │ • Push Notifications
                    │ Check            │    │ • Email Notifications
                    └──────────────────┘    │ • In-App Notifications
                                           └─────────────────┘
```

### Data Flow

1. **Trigger**: Order status change or system event
2. **Service**: NotificationService processes the event
3. **Preferences**: Check user notification settings
4. **Delivery**: Send via enabled channels
5. **Storage**: Store in-app notifications
6. **UI Update**: Update notification bell and counters

### Error Handling

- Graceful degradation when notifications are disabled
- Retry logic for failed email notifications
- Fallback to in-app notifications if push fails
- User-friendly error messages
- Logging for debugging

## Troubleshooting

### Common Issues

1. **Notifications not appearing**
   - Check browser permissions
   - Verify VAPID key configuration
   - Ensure service worker is registered

2. **Email notifications not sending**
   - Verify email configuration in `.env.local`
   - Check SMTP credentials
   - Review email service logs

3. **Permission denied**
   - Guide users to browser settings
   - Provide alternative notification methods
   - Clear explanation of benefits

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will show detailed logs in the browser console for notification events.

## Future Enhancements

- SMS notifications integration
- Notification scheduling
- Advanced filtering and grouping
- Analytics and reporting
- Multi-language notification templates
- Rich notification content with images
- Notification history persistence
- Team-based notification routing
